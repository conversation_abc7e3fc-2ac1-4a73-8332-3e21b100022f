package com.unicom.rts.integration.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/3 18:23
 * @Description
 */
@Data
public class RuleParamBean {

    String requestId;
    String requestTime;
    String startTime;
    String expireTime;
    String ruleName;
    String activityCode;
    List<String> hProvId;
    List<String> hAreaIds;
//    List<String> eparchyCodes;
    Integer groupId;
    Integer groupType;
    List<String> roamType;
    List<String> ranType;
    List<Integer> tradeAreaIds;
    List<String> areaProvId;
    List<String> areaCityId;
    List<String> brandCode;
    List<String> productIdList;
    List<String> staffId;
    List<String> ouStateList;
    String ouState;
    String durationThreshold;
    String durationRelation;

}
