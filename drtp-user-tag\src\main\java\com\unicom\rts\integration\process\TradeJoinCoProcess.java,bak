package com.unicom.rts.integration.function;

import com.unicom.realtime.bean.LacCiInfo;
import com.unicom.realtime.bean.MajorBean;
import com.unicom.realtime.bean.TradeInfo;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.util.Collector;

import java.util.HashSet;
import java.util.Set;

public class TradeJoinCoProcess extends KeyedCoProcessFunction<String, MajorBean, LacCiInfo, MajorBean> {

    private ValueState<TradeInfo> tradeInfoState;
    // metric
    private transient long joinTradeIdDelay = 0L;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        tradeInfoState = getRuntimeContext().getState(new ValueStateDescriptor<TradeInfo>("tradeInfoState", TradeInfo.class));
        //关联围栏耗时统计metric
        getRuntimeContext()
                .getMetricGroup()
                .gauge("joinTradeIdDelay", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return joinTradeIdDelay;
                    }
                });
    }

    @Override
    public void processElement1(MajorBean loc, Context context, Collector<MajorBean> collector) throws Exception {
        long ct = System.currentTimeMillis();
        TradeInfo tradeInfo = tradeInfoState.value();
        if (tradeInfo == null) {
            tradeInfo = new TradeInfo();
            Set<Integer> tradeId = new HashSet<>();
            tradeInfo.setTradeId(tradeId);
        }
//        loc.setLon(tradeInfo.getLon());
//        loc.setLat(tradeInfo.getLat());
        loc.setTradeIds(tradeInfo.getTradeId());
//        loc.setCity(tradeInfo.getArea());
//        loc.setArea(tradeInfo.getCity());
        loc.setJoinTradeTime(ct);
        collector.collect(loc);
        joinTradeIdDelay = System.currentTimeMillis() - ct;
    }

    @Override
    public void processElement2(LacCiInfo lacCiInfo, Context context, Collector<MajorBean> collector) throws Exception {
        TradeInfo tradeInfo = tradeInfoState.value();
        if (tradeInfo == null) {
            tradeInfo = new TradeInfo();
            Set<Integer> tradeId = new HashSet<>();
            tradeInfo.setTradeId(tradeId);
        }

        //c: create, u: update, d: delete, r: read
        String operation = lacCiInfo.getOperation();

        switch (operation) {
            case "r":
            case "c":
                tradeInfo.setLon(lacCiInfo.getLon());
                tradeInfo.setLat(lacCiInfo.getLat());
                tradeInfo.setArea(lacCiInfo.getArea());
                tradeInfo.setCity(lacCiInfo.getCity());
                tradeInfo.getTradeId().add(lacCiInfo.getTradeId());
                break;
            case "d":
                tradeInfo.getTradeId().remove(lacCiInfo.getTradeId());
                break;
            default:

        }
        tradeInfoState.update(tradeInfo);
    }

}
