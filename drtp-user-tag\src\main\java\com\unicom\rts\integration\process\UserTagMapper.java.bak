package com.unicom.rts.integration.function;

import com.unicom.realtime.bean.MajorBean;
import com.unicom.realtime.bean.UserTagBean;
import com.unicom.realtime.enums.UserTagBeanEnum;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2023/5/9 0:55
 * @Description
 */
public class UserTagMapper implements FlatMapFunction<RowData, MajorBean> {
    @Override
    public void flatMap(RowData row, Collector<MajorBean> out) throws Exception {
        MajorBean majorBean = new MajorBean();
        UserTagBean userTag = new UserTagBean();
        majorBean.setUserTagBean(userTag);

        String deviceNumber = toString(row.getString(UserTagBeanEnum.deviceNumber.ordinal()));
        String provId = toString(row.getString(UserTagBeanEnum.provId.ordinal()));
        String eparchyCode = toString(row.getString(UserTagBeanEnum.eparchyCode.ordinal()));
        String cityCode = toString(row.getString(UserTagBeanEnum.cityCode.ordinal()));

        userTag.setDeviceNumber(deviceNumber);
        userTag.setEparchyCode(eparchyCode);
        userTag.setProvId(provId);
        userTag.setCityCode(cityCode);

        majorBean.setSn(deviceNumber);
        majorBean.setDataSource("hudi");

        out.collect(majorBean);
    }

    public static String toString(StringData o) {
        return o == null ? null : o.toString();
    }
}

