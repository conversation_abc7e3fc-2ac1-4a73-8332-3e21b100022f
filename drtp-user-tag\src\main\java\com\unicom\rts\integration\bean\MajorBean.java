package com.unicom.rts.integration.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.flink.types.RowKind;
//import org.apache.hadoop.hbase.util.Bytes;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/1/3 20:20
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MajorBean {

    private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String SEPARATOR = "\u0001";
    private static final String UTF8_CSN = StandardCharsets.UTF_8.name();

    private String serviceItemId;
//    private String updateTime;

    private String partitionId;
    private String sn;
    private String provReg;
    private String cityReg;
    private String cityRegFromTag;
    private String provEvt;
    private String cityEvt;
    private String districtEvt; // 数据内区县编码
    private String districtReg;

    private String dataSource;
    //    private String operation;
//    private String cdhTime;
//    private String inTime;
    private String rt_b_city_code;
    private String rt_b_user_id;
    private String ranType;
    private String roamType;
    private String lac;
    private String ci;
    private String latitude;
    private String longitude;

    private String startDate;
    private String endDate;
    private String updateDate;
    private String acceptDate;
    private String acceptMonth;
    private String finishDate;
    private String receive_time; //数据接收时间
    private String root_time; //数据加工接收时间
    private String release_time; //数据下发时间
    private String integration_time; //整合层开始处理时间
    private String process_time; //数据加工层开始处理
    private String scene_id; //场景ID
    private String event_time; //数据发生时间
    private String in_time; //写入根kafka实践
    private String cdh_time; //整合层接收时间
    private String managerId = "false";   //客户经理id
    private String isMain;      //是否主客户经理
    private String isRecover;   //关系是否失效
    private String isVip;       //是否是vip用戶

    /**
     * common中的用户标签实体类
     */
    UserTagBean userTagBean;

    // hudi
    private String field0;
    private String field1;
    private String field2;
    private String field3;
    private String field4;
    private String field5;
    private String field6;
    private String field7;


    // 围栏
    long joinTradeTime; // 查hbase围栏表后填充
    Set<Integer> tradeIds; // 本条数据lac-ci归属的围栏

    // header
    @TypeInfo(MapTypeInfoFactory.class)
    private Map<String, String> headersMap = new HashMap<>();

    public void parseHeader(ConsumerRecord<byte[], byte[]> record) {
        Headers headers = record.headers();
        Iterator<Header> iterator = headers.iterator();
        while (iterator.hasNext()) {
            Header next = iterator.next();
            if (next != null) {
                String key = next.key();
                headersMap.put(key, toString(next.value()));
            }
        }
        // TODO businessId
        headersMap.put("scene_id", "realtime_tag");
        headersMap.put("root_time", Long.toString(record.timestamp()));
        headersMap.put("process_time", Long.toString(System.currentTimeMillis()));
    }

    /**
     * @param b Presumed UTF-8 encoded byte array.
     * @return String made from <code>b</code>
     */
    public static String toString(final byte[] b) {
        if (b == null) {
            return null;
        }
        return toString(b, 0, b.length);
    }

    /**
     * This method will convert utf8 encoded bytes into a string. If
     * the given byte array is null, this method will return null.
     *
     * @param b   Presumed UTF-8 encoded byte array.
     * @param off offset into array
     * @param len length of utf-8 sequence
     * @return String made from <code>b</code> or null
     */
    public static String toString(final byte[] b, int off, int len) {
        if (b == null) {
            return null;
        }
        if (len == 0) {
            return "";
        }
        try {
            return new String(b, off, len, UTF8_CSN);
        } catch (UnsupportedEncodingException e) {
            // should never happen!
            throw new IllegalArgumentException("UTF8 encoding is not supported", e);
        }
    }

    public void parseHeader2(Map<String, byte[]> record) {
        if (null != record) {
            for (Map.Entry<String, byte[]> next : record.entrySet()) {
                if (next != null) {
                    String key = next.getKey();
                    headersMap.put(key, toString(next.getValue()));
                }
            }
        }
        // TODO businessId
        headersMap.put("scene_id", "realtime_tag");
        headersMap.put("root_time", String.valueOf(kafka_in_time));
        headersMap.put("process_time", Long.toString(System.currentTimeMillis()));
    }

    public void parseHeader3(Map<String, String> record) {
        if (null != record) {
            for (Map.Entry<String, String> next : record.entrySet()) {
                if (next != null) {
                    String key = next.getKey();
                    headersMap.put(key, next.getValue());
                }
            }
        }
    }

    public void parseHeader4() {
        headersMap.put("scene_id", "realtime_tag");
        headersMap.put("root_time", String.valueOf(kafka_in_time));
        headersMap.put("process_time", Long.toString(System.currentTimeMillis()));
    }

    public Iterable<Header> getHeaders() {
        headersMap.put("release_time", Long.toString(System.currentTimeMillis()));
        RecordHeaders recordHeaders = new RecordHeaders();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.add(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    public Map<String, byte[]> getHeaders2() {
        headersMap.put("release_time", Long.toString(System.currentTimeMillis()));
        Map<String, byte[]> recordHeaders = new HashMap<>();
        headersMap.entrySet().iterator().forEachRemaining(
                entry -> recordHeaders.put(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8))
        );
        return recordHeaders;
    }

    // b
    private String userId;
    private String userIdA;
    private String tradeId;
    private String productId;
    private String brandCode;
    private String productMode; // 产品模式
//    private String PRODUCT_NAME;
//    private String PRODUCT_EXPLAIN;
//    private String GROUP_BRAND_CODE;
//    private String PRODUCT_OBJ_TYPE;
//    private String RES_TYPE_CODE;
//    private String DECLARED_PRODUCT_ID;
//    private String COMP_TAG;
//    private String ENABLE_TAG;
//    private String START_ABSOLUTE_DATE;
//    private String START_OFFSET;
//    private String START_UNIT;
//    private String END_ENABLE_TAG;
//    private String END_ABSOLUTE_DATE;
//    private String END_OFFSET;
//    private String END_UNIT;
//    private String MIN_NUMBER;
//    private String MAX_NUMBER;
//    private String CREATE_DATE;
//    private String VERSION;
//    private String PRODUCT_STATE;
//    private String UPDATE_STAFF_ID;
//    private String UPDATE_DEPART_ID;
//    private String UPDATE_TIME;
//    private String PREPAY_TAG;
//    private String NEED_EXP;
//    private String PRODUCT_APP_TYPE;
//    private String PROD_ID;
//    private String RSRV_VALUE1;
    private String RSRV_VALUE2;
//    private String RSRV_VALUE3;
//    private String RSRV_VALUE4;
//    private String RSRV_VALUE5;
//    private String OPT;
//    private String OPTTIME;
//    private String CDHTIME;
//    private String EVENT_TIME;
//    private String KAFKA_TIME;
//    private String PAIMON_TIME;

    private String itemId; // 属性标识
    private String modifyTag; // 修改标识 0和A新增
    private String tradeDepartId;
    private String tradeStaffId;
    private String subscribeState; // 订单状态
    private String tradeTypeCode; // 业务类型编码
    // redis deduplication
    private Boolean isProcessed = false;
    // expiration
    private Boolean isExpired = false;

    // kafka
    private long offset;
    private String topicAndPart;

    // json
    private SourceJsonBean sourceJsonBean;
    private String sourceJsonStr;
    private String system;
    private String table_name;
    private String operation;

    // TF_F_USER SERIAL_NUMBER、USER_ID、PROVINCE_CODE、EPARCHY_CODE、REMOVE_TAG、OPEN_DATE 、CREDIT_VALUE 、USER_STATE_CODESET、PRODUCT_ID 、NET_TYPE_CODE
    private String SERIAL_NUMBER;
    private String USER_ID;
    private String PROVINCE_CODE;
    private String EPARCHY_CODE;
    private String REMOVE_TAG;
    private String OPEN_DATE;
    private String CREDIT_VALUE;
    private String USER_STATE_CODESET;
    private String PRODUCT_ID;
    private String NET_TYPE_CODE;
    private String BRAND_CODE;
    // TF_F_USER_SP USER_ID、SP_PRODUCT_ID、START_DATE、END_DATE
    private String SP_PRODUCT_ID;
    private Set<String> productSet;
    private List<String> productList;
    private List<String> productModeList;
    private List<String> endDateList;
    private String START_DATE;
    private String END_DATE;
    private Long end_date_long;
    //    private String PRODUCT_ID;
    private String PACKAGE_ID;
    private String SP_SERVICE_ID;
    // TF_F_USER_SERVICE
    private String SERVICE_ID;
    // TD_B_DISCNT
    private String DISCNT_CODE;
    private String ATTR_CODE;
    private String ATTR_VALUE;
    // TF_F_USER_DISCNT
    private String PRIMARY_SERIAL_NUMBER;
    // TF_F_USER_RELATION
    private String MEM_USER_ID;
    private String RELATION_TYPE_CODE;
    // TF_F_USER_ITEM_SHIFT
    private String CRM_ATTR_CODE;
    private String CRM_ATTR_VALUE;
    // TF_O_LEAVEREALFEE
    private String PARTITION_ID;
    private String LEAVE_REAL_FEE;
    // TF_F_USER_PRODUCT
    private String PRODUCT_MODE;
    private Boolean is_contract_user;
    //    private Set<String> valid_main_product;
    private String valid_main_product;
    private Boolean product_is_2i2c;
    private String MAIN_PRODUCT_MONTHLY_FEE="";
    private Boolean SERVICE_IS_MONTH_RATE_LIMIT;
    private String USER_IS_ZF = "0";
    private String USER_ZHWJ_SHARES_ZF = "0";

    // meta
    private String opt;
    private String optTime;
    private String cdhtime;
    private LocalDateTime kafka_in_time;
    private LocalDateTime kafka_out_time;
    private LocalDateTime paimon_time;
    //    private String dataSource;
//    private String in_time;
    private String database_tag = "default";
    private String myDataSource;

    // 流量限速预警
    private double velolimitingFlowSaturation;

    //tf_b_trade_product
    private String subscribeId;

    // tag
    /**
     * 操作类型(透传)
     */
    private Operate operationType;
    RowKind rowKind;

    Long timeStamp;

    @Override
    public String toString() {
        // 下发字段
        // 业务流水号,受理月份,用户标识,产品模式,产品标识,品牌,属性标识,修改标志,开始时间,结束时间,A 用户标识,受理地州,服务号码,受理时间,受理渠道,受理员工,订单状态,业务类型编码,归属地州,归属省份,完工时间,受理业务区,写入根KAFKA时间,数据源,数据下发时间,数据接收时间,区县编码
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        StringBuilder sb = new StringBuilder();
        sb.append(getDatabase_tag())
                .append(SEPARATOR)
                .append(getProductSet())
                .append(SEPARATOR)
                .append(getSn())
                .append(SEPARATOR)
                .append(getUserId())
                .append(SEPARATOR)
                .append(getPROVINCE_CODE())
                .append(SEPARATOR)
                .append(getHeaders2())
                .append(SEPARATOR)
                .append(getMyDataSource())
                .append(SEPARATOR)
                .append(getOpt())
                .append(SEPARATOR)
                .append(getOptTime())
                .append(SEPARATOR)
                .append(getRowKind().toString());
//        sb.append(tradeId);
//        sb.append(SEPARATOR);
//        sb.append(acceptMonth);
//        sb.append(SEPARATOR);
//        sb.append(userId);
//        sb.append(SEPARATOR);
//        sb.append(productMode);
//        sb.append(SEPARATOR);
//        sb.append(productId);
//        sb.append(SEPARATOR);
//        sb.append(brandCode);
//        sb.append(SEPARATOR);
//        sb.append(itemId);
//        sb.append(SEPARATOR);
//        sb.append(modifyTag);
//        sb.append(SEPARATOR);
//        sb.append(startDate);
//        sb.append(SEPARATOR);
//        sb.append(endDate);
//        sb.append(SEPARATOR);
//        sb.append(userIdA);
//        sb.append(SEPARATOR);
//        sb.append(cityEvt);
//        sb.append(SEPARATOR);
//        sb.append(sn);
//        sb.append(SEPARATOR);
//        sb.append(acceptDate);
//        sb.append(SEPARATOR);
//        sb.append(tradeDepartId);
//        sb.append(SEPARATOR);
//        sb.append(tradeStaffId);
//        sb.append(SEPARATOR);
//        sb.append(subscribeState);
//        sb.append(SEPARATOR);
//        sb.append(tradeTypeCode);
//        sb.append(SEPARATOR);
//        sb.append(cityReg);
//        sb.append(SEPARATOR);
//        sb.append(provReg);
//        sb.append(SEPARATOR);
//        sb.append(finishDate);
//        sb.append(SEPARATOR);
//        sb.append(districtEvt);
//        sb.append(SEPARATOR);
//        sb.append(in_time);
//        sb.append(SEPARATOR);
//        sb.append(dataSource);
//        sb.append(SEPARATOR);
//        sb.append(simpleDateFormat.format(Long.parseLong(release_time)));
//        sb.append(SEPARATOR);
//        sb.append(simpleDateFormat.format(Long.parseLong(receive_time)));
//        sb.append(SEPARATOR);
//        sb.append(rt_b_city_code);

//        sb.append(sourceJsonBean.getProvince_code());
//        sb.append(SEPARATOR);
//        sb.append(sourceJsonBean.getEparchy_code());
//        sb.append(SEPARATOR);
//        sb.append(sourceJsonBean.getSerial_number());
//        sb.append(SEPARATOR);
//        sb.append(sourceJsonBean.getUsed_flow());
//        sb.append(SEPARATOR);
//        sb.append(sourceJsonBean.getLimit_flow());
//        sb.append(SEPARATOR);
//        sb.append(velolimitingFlowSaturation);
//        sb.append(SEPARATOR);
//        sb.append(sourceJsonBean.getEvent_time());
//        sb.append(SEPARATOR);
//        sb.append(simpleDateFormat.format(Long.parseLong(headersMap.get("receive_time"))));
//        sb.append(SEPARATOR);
//        sb.append(simpleDateFormat.format(Long.parseLong(headersMap.get("release_time"))));
//        sb.append(SEPARATOR);
//        sb.append("CB_MSG_FLOW_LIMITED_WARN");
        sb.append(sourceJsonBean);

        return sb.toString();
    }

}
