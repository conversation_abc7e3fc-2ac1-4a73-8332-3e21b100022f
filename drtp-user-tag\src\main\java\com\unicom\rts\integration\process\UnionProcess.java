package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/7/14 10:56
 * @Description
 */
@Slf4j
public class UnionProcess extends KeyedProcessFunction<String, MajorBean, MajorBean> {
    ParameterTool conf;
    private ValueState<MajorBean> userValueState;
    private ValueState<MajorBean> custManagerValueState;
    private ValueState<Integer> productCount;
    private ValueState<Boolean> userCount;
    private MapState<String, MajorBean> productMapState;
    SimpleDateFormat simpleDateFormat = null;
    SimpleDateFormat simpleDateFormat2 = null;
    OutputTag<String> printSinkTag;
    String tableName;
    boolean isUSER_IN;
    boolean isPRODUCT_IN;
    String cusSceneId;
    boolean is30In;
    private ValueState<Boolean> beenValueState;
    private ValueState<MajorBean> leaverealfeeValueState;
    List<String> removeTagList;
    String opttimeDate;
    String opttimeTime;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public UnionProcess(ParameterTool conf, OutputTag<String> ot) {
        this.conf = conf;
        printSinkTag = ot;
        tableName = conf.get("tableName", "tf_f_user_sp");
        isUSER_IN = "tf_f_user".equalsIgnoreCase(tableName) || "cmw_cust_manager_ref".equalsIgnoreCase(tableName);
        isPRODUCT_IN = "tf_f_user_product".equalsIgnoreCase(tableName);
        String[] removeTagArray = {"0", "N", "1", "3"};
        removeTagList = new ArrayList<>(Arrays.asList(removeTagArray));
        opttimeDate = conf.get("table.opttime.date", "0000-00-00");
        opttimeTime = conf.get("table.opttime.time", "00:00:00");
        cusSceneId = conf.get("cusSceneId", "");
        is30In = "is30In".equalsIgnoreCase(cusSceneId);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd:HH:mm:ss");
        userValueState = getRuntimeContext().getState(new ValueStateDescriptor<MajorBean>("userValueState", MajorBean.class));
        custManagerValueState = getRuntimeContext().getState(new ValueStateDescriptor<MajorBean>("custManagerValueState", MajorBean.class));
        productCount = getRuntimeContext().getState(new ValueStateDescriptor<Integer>("productCount", Integer.class));
        userCount = getRuntimeContext().getState(new ValueStateDescriptor<Boolean>("userCount", Boolean.class));
//        productCount.update(0);
//        listState = getRuntimeContext().getListState(new ListStateDescriptor<MajorBean>("listState", MajorBean.class));
        productMapState = getRuntimeContext().getMapState(new MapStateDescriptor<String, MajorBean>("productMapState", String.class, MajorBean.class));
        beenValueState = getRuntimeContext().getState(new ValueStateDescriptor<Boolean>("beenValueState", Boolean.class));
        leaverealfeeValueState = getRuntimeContext().getState(new ValueStateDescriptor<MajorBean>("leaverealfeeValueState", MajorBean.class));
    }

    @Override
    public void processElement(MajorBean value, KeyedProcessFunction<String, MajorBean, MajorBean>.Context ctx, Collector<MajorBean> out) throws Exception {
        // 该userId头一回来数，been设true，设定时器
        if (!isUSER_IN && (null == beenValueState.value())) {
            beenValueState.update(true);
            // 自然失效刷数，每天0点触发，刘春枝定
            ctx.timerService().registerProcessingTimeTimer(firstSecNextDayMs() + 1);
        }
        RowKind rowKind = value.getRowKind();
        if (rowKind.equals(RowKind.UPDATE_BEFORE)) {
            return;
        }
        String dataSource = value.getMyDataSource();
        String operation = value.getOpt();
        // 根据数据来源表名，做相应处理
        if ("TF_F_USER_SP".equals(dataSource)) {
            // 产品类接口，下同包括：TF_F_USER_SERVICE，TF_F_USER_DISCNT，TF_F_USER_RELATION，TF_F_USER_ITEM_SHIFT，TF_F_USER_PRODUCT
            // 主键和cb原表一样，保持业务一致
            String key = value.getUserId() + value.getPRODUCT_ID() + value.getPACKAGE_ID() + value.getSTART_DATE() + value.getSP_SERVICE_ID();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                // 删除处理，然后下发
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                // 存状态
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                // 如果主表没来，不下发，产品count置1，表示欠一条产品待下发，后续下发后再置null
                productCount.update(1);
                return;
            }
            // 如果主表已来，正常下发一条数据
        } else if ("TF_F_USER_SERVICE".equals(dataSource)) {
            String key = value.getUserId() + value.getSERVICE_ID() + value.getSTART_DATE();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                productCount.update(1);
                return;
            }
        } else if ("TF_F_USER_SERVICE_ITEM".equals(dataSource)) {
            String key = value.getATTR_CODE() + value.getCRM_ATTR_CODE() + value.getServiceItemId() + value.getSTART_DATE();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                productCount.update(1);
                return;
            }
        } else if ("TF_F_USER_DISCNT".equals(dataSource)) {
            String key = value.getUserId() + value.getPRIMARY_SERIAL_NUMBER() + value.getDISCNT_CODE() + value.getSTART_DATE();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                productCount.update(1);
                return;
            }
        } else if ("TF_F_USER_RELATION".equals(dataSource) || "TF_F_USER_RELATION_REVERSE".equals(dataSource)) {
            String key = value.getUserId() + value.getMEM_USER_ID() + value.getRELATION_TYPE_CODE() + value.getSTART_DATE();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                productCount.update(1);
                return;
            }
        } else if ("TF_F_USER_ITEM_SHIFT".equals(dataSource)) {
            if (!"30000079".equalsIgnoreCase(value.getATTR_CODE())) {
                return;
            }
            String key = value.getUserId() + value.getCRM_ATTR_CODE() + value.getATTR_CODE() + value.getSTART_DATE();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
//                productCount.update(1);
            }
            if (null == userCount.value()) {
                return;
            }
        } else if ("TF_O_LEAVEREALFEE".equals(dataSource)) {
            // 欠费接口
            // 删除处理，不下发
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                leaverealfeeValueState.update(null);
                return;
            }
            // 取原值
            MajorBean leaverealfeeValueOld = leaverealfeeValueState.value();
            // 更新状态
            leaverealfeeValueState.update(value);
            // 如果原值为空则不下发
            if (null == leaverealfeeValueOld) {
                return;
            }
            boolean isNegOld = leaverealfeeValueOld.getLEAVE_REAL_FEE().startsWith("-");
            boolean isNegNew = value.getLEAVE_REAL_FEE().startsWith("-");
            // 如果正负性不变则不下发，刘春枝定
            if (isNegOld == isNegNew) {
                return;
            }
            // 如果主表没到则不下发
            if (null == userCount.value()) {
                return;
            }
            // 如果以上都没问题，正常下发一条数据
        } else if ("TF_F_USER".equals(dataSource)) {
            // 主表接口/省分地市接口/欠费接口/产品类接口
            // 删除处理，不下发
            if (rowKind.equals(RowKind.DELETE)) {
                userValueState.update(null);
                userCount.update(null);
                return;
            }
            // 主表count置true，表示该userId主表有数据
            userCount.update(true);
            if ("Delete".equalsIgnoreCase(operation)) {
                // opt为Delete的，认为销户，remove_tag置2，更新状态，下发一条，刘春枝定
                value.setREMOVE_TAG("2");
                userValueState.update(value);
                productMapState.clear();
            } else if (!removeTagList.contains(value.getREMOVE_TAG())) {
                // 销户数据，更新状态，下发一条
                userValueState.update(value);
                productMapState.clear();
            } else {
                // 非销户常规数据
                if (isUSER_IN) {
                    // 主表接口/省分地市接口/欠费接口
                    MajorBean userValueOld = userValueState.value();
                    // 更新状态
                    userValueState.update(value);
                    if (null != userValueOld) {
                        boolean PROVINCE_CODE_b = Objects.equals(userValueOld.getPROVINCE_CODE(), value.getPROVINCE_CODE());
                        boolean SERIAL_NUMBER_b = Objects.equals(userValueOld.getSERIAL_NUMBER(), value.getSERIAL_NUMBER());
                        boolean EPARCHY_CODE_b = Objects.equals(userValueOld.getEPARCHY_CODE(), value.getEPARCHY_CODE());
                        boolean NET_TYPE_CODE_b = Objects.equals(userValueOld.getNET_TYPE_CODE(), value.getNET_TYPE_CODE());
                        boolean BRAND_CODE_b = Objects.equals(userValueOld.getBRAND_CODE(), value.getBRAND_CODE());
                        boolean OPEN_DATE_b = Objects.equals(userValueOld.getOPEN_DATE(), value.getOPEN_DATE());
                        boolean PRODUCT_ID_b = Objects.equals(userValueOld.getPRODUCT_ID(), value.getPRODUCT_ID());
                        boolean USER_STATE_CODESET_b = Objects.equals(userValueOld.getUSER_STATE_CODESET(), value.getUSER_STATE_CODESET());
                        boolean CREDIT_VALUE_b = Objects.equals(userValueOld.getCREDIT_VALUE(), value.getCREDIT_VALUE());
                        boolean REMOVE_TAG_b = Objects.equals(userValueOld.getREMOVE_TAG(), value.getREMOVE_TAG());
                        boolean unchanged = PROVINCE_CODE_b && SERIAL_NUMBER_b && EPARCHY_CODE_b && NET_TYPE_CODE_b && BRAND_CODE_b && OPEN_DATE_b && PRODUCT_ID_b && USER_STATE_CODESET_b && CREDIT_VALUE_b && REMOVE_TAG_b;
                        // 如果监控字段均未变化，则不触发下发，为刘春枝定
                        if (unchanged) {
                            return;
                        }
                    }
                    // 主表接口/省分地市接口/欠费接口，正常下发一条数据
                } else {
                    // 产品类接口
                    Integer productCount = this.productCount.value();
                    MajorBean userValueOld = userValueState.value();
                    // 更新状态
                    userValueState.update(value);
                    // REMOVE_TAG是否变化
                    boolean REMOVE_TAG_changed = false;
                    if (null != userValueOld) {
                        REMOVE_TAG_changed = !Objects.equals(userValueOld.getREMOVE_TAG(), value.getREMOVE_TAG());
                    }
                    // 是否下发
                    if (null != productCount) {
                        // 如果产品count有值，表示有欠下发，发
                    } else if (REMOVE_TAG_changed) {
                        // 如果REMOVE_TAG发生变化，可能是销户恢复，发
                    } else {
                        // 其余不发
                        return;
                    }
                    // 产品类接口，正常下发一条数据
                }
            }

        } else if ("CMW_CUST_MANAGER_REF_MAPPER".equals(dataSource)) {
            if ("DELETE".equalsIgnoreCase(operation) ||rowKind.equals(RowKind.DELETE)) {
                custManagerValueState.update(null);
            } else {
//                custManagerValueState.update(value);
                MajorBean oldValue = custManagerValueState.value();
                if (oldValue != null) {
                    if (oldValue.getEnd_date_long() <= value.getEnd_date_long()) {
                        custManagerValueState.update(value);
                    }
                } else {
                    custManagerValueState.update(value);
                }
            }
            if (null == userCount.value()) {
                return;
            }
        } else if ("TF_F_USER_PRODUCT".equals(dataSource)) {
            String key = value.getUserId() + value.getPRODUCT_ID() + value.getSTART_DATE();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                productCount.update(1);
                return;
            }
        } else if ("TF_B_TRADE_PRODUCT".equalsIgnoreCase(dataSource)) {
            if (!"1".equals(value.getModifyTag().trim()) && !"B".equals(value.getModifyTag().trim().toUpperCase())) {
                return;
            }
            String key = value.getTradeId() + value.getAcceptMonth() + value.getPRODUCT_ID() + value.getSTART_DATE() + value.getUserIdA();
            if ("Delete".equalsIgnoreCase(operation) || rowKind.equals(RowKind.DELETE)) {
                if (productMapState.contains(key)) {
                    productMapState.remove(key);
                }
            } else {
                productMapState.put(key, value);
            }
            if (null == userCount.value()) {
                productCount.update(1);
                return;
            }
        }

//        // 初始化数据不触发下发
//        if ("Init".equalsIgnoreCase(value.getOpt())) {
//            return;
//        }
        try {
            String optTime = value.getOptTime();
            String optTimeMin = opttimeDate + " " + opttimeTime;
            if (optTimeMin.compareToIgnoreCase(optTime) > 0) {
                return;
            }
        } catch (Exception e) {
            log.error("MY-ERROR-UnionProcess-opttimeParseError=", e);
        }

        if (StringUtils.isNotBlank(cusSceneId)) {
            generateOutputForCusScene(value, ctx, out, false);
        } else {
            // 触发一条下发
            generateOutput(value, ctx, out, false);
        }
    }

    @Override
    public void onTimer(long timestamp, KeyedProcessFunction<String, MajorBean, MajorBean>.OnTimerContext ctx, Collector<MajorBean> out) throws Exception {
        super.onTimer(timestamp, ctx, out);
        // 自然失效刷数
        // 主表到了的正常触发，不到的不触发
        if (null != userCount.value()) {
            if (StringUtils.isNotBlank(cusSceneId)) {
//                generateOutputForCusScene(null, ctx, out, true);
            } else {
                generateOutput(null, ctx, out, true);
            }
        }
        // 每天0点触发，刘春枝定
        ctx.timerService().registerProcessingTimeTimer(firstSecNextDayMs() + 1);
    }

    public void generateOutputForCusScene(MajorBean value, KeyedProcessFunction<String, MajorBean, MajorBean>.Context ctx, Collector<MajorBean> out, Boolean isOnTimer) throws Exception {
        if (is30In) {
            Set<String> hashSet = new HashSet<>();
            MajorBean majorBean = new MajorBean();
            if (null != value && "TF_B_TRADE_PRODUCT".equals(value.getMyDataSource().trim().toUpperCase()) && null != userCount.value()) {
                hashSet.add(value.getPRODUCT_ID() + "|" + value.getPaimon_time().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                Iterator<Map.Entry<String, MajorBean>> iterator = productMapState.iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, MajorBean> entry = iterator.next();
                    String endDate = String.valueOf(dateFormat.parse(entry.getValue().getPaimon_time().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).getTime());
                    //当前日期-入库时间=30天的数据进行下发
                    if ((System.currentTimeMillis() - Long.valueOf(endDate)) / 1000 / 60 / 60 / 24 <= 60) {
                        hashSet.add(entry.getValue().getPRODUCT_ID() + "|" + entry.getValue().getPaimon_time().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    iterator.remove();
                }
                if (hashSet.size() > 0) {
                    majorBean.setSystem("9900_2i");
                    majorBean.setSn(userValueState.value().getSERIAL_NUMBER());
                    majorBean.setUserId(ctx.getCurrentKey());
                    majorBean.setPROVINCE_CODE(userValueState.value().getPROVINCE_CODE());
                    majorBean.setProductSet(hashSet);
                    if (null != value) {
                        majorBean.parseHeader3(value.getHeadersMap());
                        majorBean.setDatabase_tag(value.getDatabase_tag());
                        majorBean.setOpt(value.getOpt());
                        majorBean.setRowKind(value.getRowKind());
                        majorBean.setOptTime(value.getOptTime());
                        majorBean.setMyDataSource(value.getMyDataSource());
                    }
                    out.collect(majorBean);
                    //sideOutput
                    ctx.output(printSinkTag, majorBean.toString());
                }
            } else if (null != value && "TF_B_TRADE_PRODUCT".equals(value.getMyDataSource().trim().toUpperCase()) && null == userCount.value()) {
                return;
            }
        }
    }

    // 触发下发，包括正常触发和定时器触发
    public void generateOutput(MajorBean value, KeyedProcessFunction<String, MajorBean, MajorBean>.Context ctx, Collector<MajorBean> out, Boolean isOnTimer) throws Exception {
        boolean hasExp = false;
        boolean is_contract_user = false;
        String valid_main_product = "";
        Set<String> hashSet = new HashSet<>();
        List<MajorBean> tempProductList = new ArrayList<>();
        List<String> productList = new ArrayList<>();
        List<String> productModeList = new ArrayList<>();
        List<String> endDateList = new ArrayList<>();
        boolean isService50027 = false;
        boolean isServiceItemPlicyState02 = false;
        String user_is_zf = "";
        String user_zhwj_shares_zf = "";

        // 遍历产品MapState，剔除过期产品
        Iterator<Map.Entry<String, MajorBean>> iterator = productMapState.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, MajorBean> entry = iterator.next();
            long end_date_l = entry.getValue().getEnd_date_long();

            long currentTimeMillis = System.currentTimeMillis();
            if (currentTimeMillis < end_date_l) {
                // 未过期
                switch (tableName) {
                    case "tf_f_user_sp":
                        hashSet.add(entry.getValue().getSP_PRODUCT_ID());
                        break;
                    case "tf_f_user_service":
                        if ("TF_F_USER_SERVICE".equals(entry.getValue().getMyDataSource())) {
                            hashSet.add(entry.getValue().getSERVICE_ID());
                            if ("50027".equals(entry.getValue().getSERVICE_ID().trim())) {
                                isService50027 = true;
                            }
                        } else if ("TF_F_USER_SERVICE_ITEM".equals(entry.getValue().getMyDataSource())) {
                            if ("policyState".equals(entry.getValue().getCRM_ATTR_CODE())) {
                                if ("0".equals(entry.getValue().getCRM_ATTR_VALUE()) || "2".equals(entry.getValue().getCRM_ATTR_VALUE())) {
                                    if (entry.getValue().getEnd_date_long() > currentTimeMillis) {
                                        isServiceItemPlicyState02 = true;
                                    }
                                }
                            }
                        }
                        break;
                    case "tf_f_user_discnt":
                        hashSet.add(entry.getValue().getDISCNT_CODE());
                        break;
                    case "tf_f_user_relation":
                        String relationTypeCode = entry.getValue().getRELATION_TYPE_CODE();
                        String memUserId = entry.getValue().getMEM_USER_ID();
                        String primaryUserId = entry.getValue().getUserId();
                        long endDate = entry.getValue().getEnd_date_long();
                        if (StringUtils.isNotBlank(relationTypeCode) && (relationTypeCode.toUpperCase().equals("HZFK")
                                || relationTypeCode.toUpperCase().equals("KYZF") || relationTypeCode.toUpperCase().equals("ZF"))) {
                            if (primaryUserId.trim().equals(memUserId.trim()) && endDate > System.currentTimeMillis()) {
                                user_is_zf = "1";
                            } else if (!(primaryUserId.trim().equals(memUserId.trim())) && endDate > System.currentTimeMillis()) {
                                if (StringUtils.isBlank(user_is_zf)) {
                                    user_is_zf = "2";
                                }
                            }
                        }

                        if (StringUtils.isNotBlank(relationTypeCode) && (relationTypeCode.toUpperCase().equals("8800")
                                || relationTypeCode.toUpperCase().equals("8801") || relationTypeCode.toUpperCase().equals("8803"))
                                || relationTypeCode.toUpperCase().equals("8806") || relationTypeCode.toUpperCase().equals("8807")) {
                            if (primaryUserId.trim().equals(memUserId.trim()) && endDate > System.currentTimeMillis()) {
                                user_zhwj_shares_zf = "1";
                            } else if (!(primaryUserId.trim().equals(memUserId.trim())) && endDate > System.currentTimeMillis()) {
                                if (StringUtils.isBlank(user_zhwj_shares_zf)) {
                                    user_zhwj_shares_zf = "2";
                                }
                            }
                            if (user_zhwj_shares_zf.toUpperCase().equals("2") && endDate > System.currentTimeMillis() && user_is_zf.toUpperCase().equals("1")) {
                                user_zhwj_shares_zf = "1";
                            }
                        }

                        hashSet.add(entry.getValue().getMEM_USER_ID());
                        break;
                    case "tf_f_user":
                        hashSet.add(entry.getValue().getATTR_VALUE());
                        break;
                    case "tf_f_user_product":
                        tempProductList.add(entry.getValue());
                        String product_mode = entry.getValue().getPRODUCT_MODE();
                        if ("50".equals(product_mode)) {
                            is_contract_user = true;
                        }
                        if ("00".equals(product_mode)) {
                            String start_date = entry.getValue().getSTART_DATE();
                            long start_date_l = 0L;
                            try {
                                // date format conversion
                                if (start_date.contains(":")) {
                                    if (start_date.contains(" ")) {
                                        start_date_l = simpleDateFormat.parse(start_date).getTime();
                                    } else {
                                        start_date_l = simpleDateFormat2.parse(start_date).getTime();
                                    }
                                } else {
                                    start_date_l = Long.parseLong(start_date);
                                }
                                if (start_date_l < 1000000000000L) {
                                    throw new Exception("MY-ERROR-START_DATE: start_date < 1000000000000L Exception!");
                                }
                            } catch (Exception e) {
                                log.error("MY-ERROR-START_DATE-parseError=" + e);
                                return;
                            }
                            if (start_date_l < currentTimeMillis) {
//                                valid_main_product.add(product_id);
                                valid_main_product = entry.getValue().getPRODUCT_ID();
                            }

                        }
                        break;
                    default:
                }

            } else {
                // 过期
                // 定时器触发自然过期刷数下，标识该userId存在自然过期
                if (isOnTimer) {
                    hasExp = true;
                }
                // 剔除
                iterator.remove();
            }
        }

        if (isPRODUCT_IN) {
            // 排序以确保列表顺序一致
            tempProductList.sort(Comparator.comparing(MajorBean::getPRODUCT_ID)
                .thenComparing(MajorBean::getSTART_DATE, Comparator.nullsLast(String::compareTo)));

            for (MajorBean bean : tempProductList) {
                String product_id = bean.getPRODUCT_ID();
                hashSet.add(product_id);
                // 将三个字段拼接成一个字符串，用'##_##'分隔，以保证原子性
                String combinedProductInfo = bean.getPRODUCT_ID() + "##_##" + bean.getPRODUCT_MODE() + "##_##" + bean.getEND_DATE();
                productList.add(combinedProductInfo);
            }
        }

        // 定时器触发自然过期刷数下，如果没有过期，则不下发
        if (isOnTimer && !hasExp) {
            return;
        }
//        String res = "print="+System.currentTimeMillis()+"||source="+dataSource+"||uid="+ctx.getCurrentKey()+"||spid="+hashSet.toString()+"||sn="+tradeInfoState.value().getSERIAL_NUMBER()+"||prov="+tradeInfoState.value().getPROVINCE_CODE();

        // 产品类接口的，下发一次后，产品count置null，表示今后不欠下发，只产品表触发下发，主表不触发下发。
        // 主表/省分地市/欠费接口的，只要主表和属性表都具备，每次来均触发下发，所以产品count保持1，这样不阻止主表触发下发
        if (!isUSER_IN) {
            productCount.update(null);
        }

        // 获取主表状态
        MajorBean userValue = userValueState.value();

        // 生成下发对象
        MajorBean majorBean = new MajorBean();
        majorBean.setSn(userValue.getSERIAL_NUMBER());
        majorBean.setUserId(ctx.getCurrentKey());
        majorBean.setPROVINCE_CODE(userValue.getPROVINCE_CODE());
        majorBean.setEPARCHY_CODE(userValue.getEPARCHY_CODE());
        majorBean.setNET_TYPE_CODE(userValue.getNET_TYPE_CODE());
        majorBean.setREMOVE_TAG(userValue.getREMOVE_TAG());
        majorBean.setBRAND_CODE(userValue.getBRAND_CODE());
        majorBean.setProductSet(hashSet);
        majorBean.setProductList(new ArrayList<>(productList));
        majorBean.setProductModeList(new ArrayList<>());
        majorBean.setEndDateList(new ArrayList<>());
        majorBean.setSERVICE_IS_MONTH_RATE_LIMIT(isService50027 && isServiceItemPlicyState02);
        majorBean.setUSER_IS_ZF(user_is_zf);
        majorBean.setUSER_ZHWJ_SHARES_ZF(user_zhwj_shares_zf);

        if (null != custManagerValueState.value()) {
            //用户客户经理关系
            MajorBean custManagerRel = custManagerValueState.value();
            if (StringUtils.isNotBlank(custManagerRel.getIsRecover()) && "0".equals(custManagerRel.getIsRecover())
                    && StringUtils.isNotBlank(custManagerRel.getIsMain()) && "1".equals(custManagerRel.getIsMain())
                    && StringUtils.isNotBlank(custManagerRel.getIsVip()) && "1".equals(custManagerRel.getIsVip())) {
                majorBean.setManagerId("true");
            } else {
                majorBean.setManagerId("false");
            }
        }else{
            majorBean.setManagerId("false");
        }


        // 主表/省分地市/欠费接口的，有更多额外字段
        if (isUSER_IN) {
            majorBean.setOPEN_DATE(userValue.getOPEN_DATE());
            majorBean.setPRODUCT_ID(userValue.getPRODUCT_ID());
            majorBean.setUSER_STATE_CODESET(userValue.getUSER_STATE_CODESET());
            majorBean.setCREDIT_VALUE(userValue.getCREDIT_VALUE());
            MajorBean leaverealfeeValue = leaverealfeeValueState.value();
            if (null != leaverealfeeValue) {
                majorBean.setLEAVE_REAL_FEE(leaverealfeeValue.getLEAVE_REAL_FEE());
            } else {
                majorBean.setLEAVE_REAL_FEE(null);
            }
        }

        // product_in接口的，有更多额外字段
        if (isPRODUCT_IN) {
            majorBean.setPRODUCT_ID(userValue.getPRODUCT_ID());
            majorBean.setIs_contract_user(is_contract_user);
            majorBean.setValid_main_product(valid_main_product);
        }

        // 元数据字段
        if (null != value) {
            // 正常触发
            majorBean.parseHeader3(value.getHeadersMap());
            majorBean.setDatabase_tag(value.getDatabase_tag());
            majorBean.setMyDataSource(value.getMyDataSource());
            majorBean.setOpt(value.getOpt());
            majorBean.setRowKind(value.getRowKind());
            majorBean.setOptTime(value.getOptTime());
        } else {
            // 定时器触发
            majorBean.setMyDataSource("Timer");
            majorBean.setOpt("Timer");
            majorBean.setRowKind(RowKind.INSERT);
        }

        // 下发
        out.collect(majorBean);
        // sideOutput
//        ctx.output(printSinkTag, res);

    }

    public static long firstSecNextDayMs() {
//        return now - (now + timeZone * 3600000) % 86400000 + 86400000;
        Calendar cale = null;
        cale = Calendar.getInstance();
        cale.add(Calendar.DATE, 1);
        cale.set(Calendar.HOUR_OF_DAY, 0);
        cale.set(Calendar.MINUTE, 0);
        cale.set(Calendar.SECOND, 0);
        cale.set(Calendar.MILLISECOND, 0);
        return cale.getTime().getTime();
    }

    public static long firstSecNextMonthMs() {
//        return now - (now + timeZone * 3600000) % 86400000 + 86400000;
        Calendar cale = null;
        cale = Calendar.getInstance();
        // first sec next month
        cale.add(Calendar.MONTH, 1);
        cale.set(Calendar.DAY_OF_MONTH, 1);
        cale.set(Calendar.HOUR_OF_DAY, 0);
        cale.set(Calendar.MINUTE, 0);
        cale.set(Calendar.SECOND, 0);
        cale.set(Calendar.MILLISECOND, 0);
        return cale.getTime().getTime();
    }

    public static long lastSecLastMonthMs() {
        Calendar cale = null;
        cale = Calendar.getInstance();
        // last sec last month
        cale.set(Calendar.DAY_OF_MONTH, 0);
        cale.set(Calendar.HOUR_OF_DAY, 23);
        cale.set(Calendar.MINUTE, 59);
        cale.set(Calendar.SECOND, 59);
        cale.set(Calendar.MILLISECOND, 0);
        return cale.getTime().getTime();
    }


}
