package com.unicom.rts.integration.function;

import com.unicom.realtime.bean.MajorBean;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.tuple.Tuple4;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/2/10 19:40
 * @Description
 */
public class TimerProcess extends KeyedProcessFunction<String, Tuple4<String, MajorBean, Iterable<Header>, Integer>, Tuple3<String, String, Iterable<Header>>> {

    private final static Logger logger = LoggerFactory.getLogger(TimerProcess.class);
    private ParameterTool conf;
    private ValueState<String> timeState;
//    private ValueState<Tuple4<String, MajorBean, Iterable<Header>, Integer>> t3State;
    private transient long duplicated = 0L;
    public TimerProcess(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        ValueStateDescriptor<String> time = new ValueStateDescriptor<>("timeState",String.class);
        timeState = getRuntimeContext().getState(time);

//        ValueStateDescriptor<Tuple4<String, MajorBean, Iterable<Header>, Integer>> vsd = new ValueStateDescriptor<>("t3State", Types.TUPLE(Types.STRING,Types.POJO(MajorBean.class),Types.GENERIC(Header.class),Types.INT));
//        t3State = getRuntimeContext().getState(vsd);
        // metric init
        getRuntimeContext()
                .getMetricGroup()
                .gauge("myDuplicated", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return duplicated;
                    }
                });

    }

    @Override
    public void processElement(Tuple4<String, MajorBean, Iterable<Header>, Integer> value, Context ctx, Collector<Tuple3<String, String, Iterable<Header>>> out) throws Exception {
        //判断是否存在定时器，存在则过滤；不存在，则注册当前时间戳+interval
        if(timeState.value()==null || "".equals(timeState.value()) || timeState.value().isEmpty()) {
            long interval=(long)(conf.getFloat("deduplication.interval.hour",48F) * 60 * 60 * 1000F);
            timeState.update(String.valueOf(ctx.timerService().currentProcessingTime()));
            ctx.timerService().registerProcessingTimeTimer(Long.parseLong(timeState.value())+interval);
            if (value.f1.getIsProcessed()) { return; }
            switch (value.f3) {
                // TODO 更新业务逻辑
                case 50:
                    out.collect(new Tuple3<>(value.f0, value.f1.toString(), value.f2));
                    // TODO remove before prod
                    logger.info("MY-INFO-SINK!!!Topic=" + value.f0 + "||Data=" + value.f1.toString());
                    break;
                default:
                    logger.error("MY-ERROR-TimerProcess switch default!!! business id="+ value.f3);
            }
        } else {
            duplicated++;
            switch (value.f3) {
                case 50:
//                    logger.info("MY-INFO-TimerProcess skipped!!!Topic=" + value.f0 + "||Data=" + value.f1.toString());
                    break;
                default:
                    logger.error("MY-ERROR-TimerProcess switch default2!!! business id="+ value.f3);
            }
        }
    }

    @Override
    public void close() throws Exception {}

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Tuple3<String, String, Iterable<Header>>> out) throws Exception {
//        t3State.clear();
        timeState.clear();
    }

}
