package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.Iterator;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023/7/14 10:56
 * @Description
 */
@Slf4j
public class BroadcastProcess extends KeyedBroadcastProcessFunction<String, MajorBean, MajorBean, MajorBean> {
    ParameterTool conf;
    OutputTag<String> printSinkTag;
    String tableName;
    boolean isDiscnt;
    boolean isProduct;
    final MapStateDescriptor<String, String> tdState = new MapStateDescriptor<>("tdState", Types.STRING, Types.STRING);
    public BroadcastProcess(ParameterTool conf, OutputTag<String> ot) {
        this.conf = conf;
        printSinkTag = ot;
        tableName = conf.get("tableName", "tf_f_user_sp");
        isDiscnt = "tf_f_user_discnt".equalsIgnoreCase(tableName);
        isProduct = "tf_f_user_product".equalsIgnoreCase(tableName);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(MajorBean value, ReadOnlyContext ctx, Collector<MajorBean> out) throws Exception {
        ReadOnlyBroadcastState<String, String> broadcastState = ctx.getBroadcastState(tdState);
        if (isDiscnt) {
            Set<String> product_id_hashset = value.getProductSet();
            Iterator<String> iterator = product_id_hashset.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                if (!broadcastState.contains(next)) {
                    iterator.remove();
                }
            }
            // necessary or not?
//            value.setSP_PRODUCT_ID_hashset(product_id_hashset);
        } else if (isProduct) {
            boolean product_is_2i2c = false;
            Set<String> product_id_hashset = value.getProductSet();
            Iterator<String> iterator = product_id_hashset.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                if (broadcastState.contains(next)) {
                    product_is_2i2c = true;
                }
            }
            if (StringUtils.isBlank(value.getSn())) {
                product_is_2i2c = false;
            }
            value.setProduct_is_2i2c(product_is_2i2c);
        }

        out.collect(value);
//        String res = "print="+System.currentTimeMillis()+"||source="+value.getDataSource()+"||uid="+value.getUserId()+"||productid="+product_id_hashset.toString()+"||sn="+value.getSn()+"||prov="+value.getPROVINCE_CODE();
//        log.info("MY-INFO-BroadcastProcess-"+res);
    }

    @Override
    public void processBroadcastElement(MajorBean value, Context ctx, Collector<MajorBean> out) throws Exception {
        BroadcastState<String, String> broadcastState = ctx.getBroadcastState(tdState);
        if (isDiscnt) {
            String discnt_code = value.getDISCNT_CODE();
            String attr_code = value.getATTR_CODE();
            if (!"lowCostRange".equalsIgnoreCase(attr_code)) {
                return;
            }
            if ("Delete".equalsIgnoreCase(value.getOpt()) || value.getRowKind().equals(RowKind.DELETE)) {
                broadcastState.remove(discnt_code);
            }
            broadcastState.put(discnt_code, value.getATTR_VALUE());
        } else if (isProduct) {
            String product_id = value.getPRODUCT_ID();
            String attr_code = value.getATTR_CODE();
            String attr_value = value.getATTR_VALUE();
            if (!"2I2C".equalsIgnoreCase(attr_code) || !"2I2C".equalsIgnoreCase(attr_value)) {
                return;
            }
            if ("Delete".equalsIgnoreCase(value.getOpt()) || value.getRowKind().equals(RowKind.DELETE)) {
                broadcastState.remove(product_id);
            }
            broadcastState.put(product_id, attr_code);
        }
    }
}
