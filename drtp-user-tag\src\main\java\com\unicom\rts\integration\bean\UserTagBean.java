package com.unicom.rts.integration.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/2/16 11:24
 * @Description
 */
@Data
public class UserTagBean {

    private String deviceNumber; //设备号
    private String k000001;      //性别
    private String k000002;      //年龄
    private String k000004;      //在网时长
    private String k000006;      //客户类型
    private String k000046;      //归属省分
    private String k000055;      //是否极低使用量
    private String k000056;      //是否出账用户
    private String k000057;      //是否三无
    private String k000058;      //产品类型
    private String k000060;      //合约类型
    private String k000061;      //合约时长
    private String k000062;      //合约终止日期
    private String k000064;      //是否上网卡用户
    private String k000065;      //套餐类型
    private String k000066;      //是否主副卡
    private String k003681;      //号码归属地市（O域）
    private String k003711;      //融合套餐宽带账号
    private String k002436;      //号码归属地市（B域）
    private String k003589;      //入网时间（日）
    private String k002902;      //用户标识

    private String userIdBus;                     //用户标识
    private String eparchyCodeBus;                //归属地州
//    private String finishDate;                 //完成时间
    private String provIdBus;                     //发生省分id
//    private String eparchyCodeFinal;                     //优先取实时标签的归属地市
//    private String provIdFinal;
//    private String userIdFinal;
//    private RtuserTagBean rtuserTagBean;

    // hudi
    private String field0;
    private String field1;
    private String field2;
    private String field3;
    private String field4;
    private String field5;

    private String provId;
    private String eparchyCode;
    private String cityCode;


}
