package com.unicom.rts.integration.bean;

import lombok.Data;

@Data
public class LacCiInfo {
    String lacCi;
    String lon;
    String lat;
    String area;
    String city;
    String operation;
    Integer tradeId;

    public LacCiInfo(String lacCi, String lon, String lat, String area, String city, Integer tradeId, String operation) {
        this.lacCi = lacCi;
        this.lon = lon;
        this.lat = lat;
        this.area = area;
        this.city = city;
        this.tradeId = tradeId;
        this.operation = operation;

    }

    @Override
    public String toString() {
        return "LacCiInfo{" +
                "lacCi='" + lacCi + '\'' +
                ", lon='" + lon + '\'' +
                ", lat='" + lat + '\'' +
                ", area='" + area + '\'' +
                ", city='" + city + '\'' +
                ", operation='" + operation + '\'' +
                ", tradeId=" + tradeId +
                '}';
    }
}
