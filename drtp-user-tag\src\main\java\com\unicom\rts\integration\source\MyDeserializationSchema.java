package com.unicom.rts.integration.source;

import com.alibaba.fastjson.JSON;
import com.unicom.rts.integration.bean.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Date 2023/5/5 17:42
 * @Description
 */
public class MyDeserializationSchema implements KafkaRecordDeserializationSchema<MajorBean> {
    private final static Logger log = LoggerFactory.getLogger(MyDeserializationSchema.class);

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<MajorBean> out) throws IOException {
        try {
            MajorBean bal = new MajorBean();
            bal.setOffset(record.offset());
            bal.setTopicAndPart(StringUtils.join(record.topic(), record.partition()));
            parse(bal, record);
            //增加头
            bal.parseHeader(record);
            bal.setTimeStamp(record.timestamp());

            out.collect(bal);
        } catch (Exception e) {
            log.error("MY-ERROR-deserialize error=", e);
        }
    }

    @Override
    public TypeInformation<MajorBean> getProducedType() {
        return TypeInformation.of(new TypeHint<MajorBean>() {
        });
    }

    public void parse(MajorBean aucBean, ConsumerRecord<byte[], byte[]> record) throws Exception {
        String str = new String(record.value(), StandardCharsets.UTF_8);
//        String[] cols = StringUtils.splitPreserveAllTokens(str, "\001");
        aucBean.setSourceJsonStr(str);
        SourceJsonBean jsonRootBean = JSON.parseObject(str, SourceJsonBean.class);
        aucBean.setSourceJsonBean(jsonRootBean);
        aucBean.setSn(jsonRootBean.getSERIAL_NUMBER());
        aucBean.setUserId(jsonRootBean.getUSER_ID());
        aucBean.setProvReg(jsonRootBean.getPROVINCE_CODE());
        aucBean.setCityReg(jsonRootBean.getEPARCHY_CODE());

        aucBean.setSystem(jsonRootBean.getSystem());
        aucBean.setTable_name(jsonRootBean.getTable_name());
        String operation = jsonRootBean.getOperation();
        if (StringUtils.isBlank(operation)) {
            throw new Exception("MY-ERROR-operationIsBlank!");
        }
        aucBean.setOperation(jsonRootBean.getOperation());
        aucBean.setOperationType(Operate.load(operation.getBytes()[0]));
        aucBean.setSERIAL_NUMBER(jsonRootBean.getSERIAL_NUMBER());
        aucBean.setUSER_ID(jsonRootBean.getUSER_ID());
        aucBean.setPROVINCE_CODE(jsonRootBean.getPROVINCE_CODE());
        aucBean.setEPARCHY_CODE(jsonRootBean.getEPARCHY_CODE());
        aucBean.setREMOVE_TAG(jsonRootBean.getREMOVE_TAG());
        aucBean.setOPEN_DATE(jsonRootBean.getOPEN_DATE());
        aucBean.setCREDIT_VALUE(jsonRootBean.getCREDIT_VALUE());
        aucBean.setUSER_STATE_CODESET(jsonRootBean.getUSER_STATE_CODESET());
        aucBean.setPRODUCT_ID(jsonRootBean.getPRODUCT_ID());
        aucBean.setNET_TYPE_CODE(jsonRootBean.getNET_TYPE_CODE());
        aucBean.setSP_PRODUCT_ID(jsonRootBean.getSP_PRODUCT_ID());
        aucBean.setSTART_DATE(jsonRootBean.getSTART_DATE());
        aucBean.setEND_DATE(jsonRootBean.getEND_DATE());
//        aucBean.setScene_code(cols[0]);// 场景编码
//        aucBean.setOrder_id(cols[1]);// 订单号(自助)
//        aucBean.setSerial_number(cols[2]);// 电话号码
//        aucBean.setStatus(cols[3]);// 号码状态
//        aucBean.setUsername(cols[4]);// 姓名
//        aucBean.setBalance(cols[5]);// 剩余话费
//        aucBean.setOwe_fee(cols[6]);// 欠费金额
//        aucBean.setIs_contract_prod(cols[7]);// 是否合约期产品
//        aucBean.setProvince_code(cols[8]);// 省份编码
//        aucBean.setEparchy_code(cols[9]);// 地市编码
//        aucBean.setEnter_time(cols[10]);// 进入时间（办理步骤的时间）
//        aucBean.setVisit_time(cols[11]);// 访问时间
//        aucBean.setCheck_result(cols[12]);// 校验结果
//        aucBean.setRemove_reason(cols[13]);// 销户原因
//        aucBean.setError_code(cols[14]);// 报错编码
//        aucBean.setError_info(cols[15]);// 报错原因
//        aucBean.setMain_product_id(cols[16]);// 主套餐id
//        aucBean.setMain_product_name(cols[17]);// 主套餐名称
//        aucBean.setBal_trans_method(cols[18]);// 余额转移方式
//        aucBean.setTrans_bank_card(cols[19]);// 退费银行卡号
//        aucBean.setTrans_mobil_no(cols[20]);// 退费手机号码（联通）
//        aucBean.setTrans_wo_account(cols[21]);// 退费沃账户
//        aucBean.setTrans_wo_acct_name(cols[22]);// 退费沃账户名称
//        aucBean.setRecv_user_name(cols[23]);// 收款人姓名
//        aucBean.setTrans_contract_tel(cols[24]);// 退费联系人电话
//        aucBean.setHold_or_stay(cols[25]);// 进入维系还是留单
//        aucBean.setTrade_error_no(cols[26]);// 办理失败编码
//        aucBean.setData_receive_time(cols[27]);// 数据接收时间
//        aucBean.setData_source(cols[28]);// 数据源
//        aucBean.setData_rootkafka_time(cols[29]);// 写入根KAFKA时间
        // aucBean.setOuttime(cols[30]);// 实时场景中心下发时间
//        System.out.println("MyDeserializationSchema="+jsonRootBean);
    }
}

