package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
@Slf4j
public class TF_F_USER_SERVICE_Mapper extends RichFlatMapFunction<Row, MajorBean> {
    ParameterTool conf;
    Long end_date_earliest;
    SimpleDateFormat simpleDateFormat = null;
    SimpleDateFormat simpleDateFormat2 = null;
    public TF_F_USER_SERVICE_Mapper(ParameterTool conf) {
        this.conf = conf;
        end_date_earliest = Long.parseLong(conf.get("table.end_date.ms", "1698854400000"));
        simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd:HH:mm:ss");
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        long currentTimeMillis = System.currentTimeMillis();
        // PARTITION_ID,USER_ID,SERVICE_ID,MAIN_TAG,START_DATE,END_DATE,SERVICE_ITEM_ID,PACKAGE_ID,PRODUCT_ID,UPDATE_TIME,EPARCHY_CODE,PROVINCE_CODE,PRIOR_ORDER_TIME,opt,optTime,cdhtime

        String end_date = String.valueOf(value.getField("END_DATE"));
        String opt = String.valueOf(value.getField("opt"));
        long end_date_l = 0L;
        try {
            // date format conversion
            if (end_date.contains(":")) {
                if (end_date.contains(" ")) {
                    end_date_l = simpleDateFormat.parse(end_date).getTime();
                } else {
                    end_date_l = simpleDateFormat2.parse(end_date).getTime();
                }
            } else {
                end_date_l = Long.parseLong(end_date);
            }
            if (end_date_l < 1000000000000L) {
                throw new Exception("MY-ERROR-SubMapper: end_date < 1000000000000L Exception!");
            }
        } catch (Exception e) {
            log.error("MY-ERROR-SubMapper-parseError="+e);
            return;
        }
        if ("Init".equals(opt) && end_date_l < end_date_earliest) {
            return;
        }

        String user_id = String.valueOf(value.getField("USER_ID"));
        if (StringUtils.isBlank(user_id)) {
            return;
        }

        MajorBean majorBean = new MajorBean();
        majorBean.setUserId(user_id);
        majorBean.setSERVICE_ID(String.valueOf(value.getField("SERVICE_ID")));
        majorBean.setSTART_DATE(String.valueOf(value.getField("START_DATE")));
        majorBean.setEND_DATE(end_date);
        majorBean.setEnd_date_long(end_date_l);
        majorBean.setMyDataSource("TF_F_USER_SERVICE");
        majorBean.setRowKind(value.getKind());
        majorBean.setOpt(opt);
        majorBean.setOptTime(String.valueOf(value.getField("opttime")));
//                majorBean.setCdhtime(String.valueOf(value.getField("cdhtime")));
        majorBean.setKafka_in_time((LocalDateTime) value.getField("kafka_in_time"));
//                majorBean.setKafka_out_time(String.valueOf(value.getField("kafka_out_time")));
//                majorBean.setPaimon_time(String.valueOf(value.getField("paimon_time")));
        majorBean.parseHeader2((Map<String, byte[]>) value.getField("headers"));
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));
        out.collect(majorBean);
    }
}
