package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
public class TD_B_DISCNT_Mapper extends RichFlatMapFunction<Row, MajorBean> {
    ParameterTool conf;
    public TD_B_DISCNT_Mapper(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        // DISCNT_CODE,ATTR_CODE,ATTR_VALUE,AREA_CODE,UPDATE_STAFF_ID,UPDATE_DEPART_ID,UPDATE_TIME,opt,optTime,cdhtime
        long currentTimeMillis = System.currentTimeMillis();
        MajorBean majorBean = new MajorBean();
        majorBean.setDISCNT_CODE(String.valueOf(value.getField("DISCNT_CODE")));
        majorBean.setATTR_CODE(String.valueOf(value.getField("ATTR_CODE")));
        majorBean.setATTR_VALUE(String.valueOf(value.getField("ATTR_VALUE")));
        majorBean.setMyDataSource("TD_B_DISCNT");
        majorBean.setRowKind(value.getKind());
        majorBean.setOpt(String.valueOf(value.getField("opt")));
        majorBean.setOptTime(String.valueOf(value.getField("opttime")));
//                majorBean.setCdhtime(String.valueOf(value.getField("cdhtime")));
        majorBean.setKafka_in_time((LocalDateTime) value.getField("kafka_in_time"));
//                majorBean.setKafka_out_time(String.valueOf(value.getField("kafka_out_time")));
//                majorBean.setPaimon_time(String.valueOf(value.getField("paimon_time")));
        majorBean.parseHeader2((Map<String, byte[]>) value.getField("headers"));
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));
        out.collect(majorBean);
    }
}
