package com.unicom.rts.integration.bean;

public enum Operate {

    /**
     * 枚举操作类型：U表示更新，I表示插入，D表示删除，K表示插入主键
     */

    Update((byte) 'U'), Insert((byte) 'I'), Delete((byte) 'D'), Key((byte) 'K');

    private byte tag;

    private Operate(byte tag) {
        this.tag = tag;
    }

    public static Operate load(byte tag) {
        for (Operate ins : Operate.values()) {
            if (ins.tag == tag) {
                return ins;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "Operate{" +
                "tag=" + tag +
                '}';
    }
}