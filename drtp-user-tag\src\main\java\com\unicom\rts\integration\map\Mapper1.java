package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import com.unicom.rts.integration.utils.MyRedisPool;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisCluster;

/**
 * <AUTHOR>
 * @Date 2023/1/3 20:33
 * @Description
 */
public class Mapper1 extends RichFlatMapFunction<MajorBean, MajorBean> {

    private final ParameterTool conf;
    private final static Logger logger = LoggerFactory.getLogger(Mapper1.class);
//    private transient long delay = 0L;
    private transient long duplicated = 0L;
    private transient long expired = 0L;
    JedisCluster jedisCluster = null;
    private String groupId;

    public Mapper1(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // metric init
//        getRuntimeContext()
//                .getMetricGroup()
//                .gauge("myDelay", new Gauge<Long>() {
//                    @Override
//                    public Long getValue() {
//                        return delay;
//                    }
//                });
        getRuntimeContext()
                .getMetricGroup()
                .gauge("myDuplicated", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return duplicated;
                    }
                });
        getRuntimeContext()
                .getMetricGroup()
                .gauge("myExpired", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return expired;
                    }
                });
        // redis init
        jedisCluster = MyRedisPool.getJedisPool(conf);
        groupId = conf.get("source.kafka.properties.group","sscj-smsWarn-test-20230517");

    }

    @Override
    public void flatMap(MajorBean value, Collector<MajorBean> out) throws Exception {


//        try {
//            delay = System.currentTimeMillis() - value.timestamp();
//        } catch (Exception e) {
//            logger.error("MY-ERROR-mapper=" + e);
//        }
        // expire filter
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        long eventTime = simpleDateFormat.parse(value.getSourceJsonBean().getEvent_time()).getTime();
//        long tsDiff = System.currentTimeMillis() - eventTime;
//        if (tsDiff > conf.getLong("filter.diff.hours",48L) * 60 * 60 * 1000L) {
//            value.setIsExpired(true);
//            expired++;
//            return;
//        }
        // redis deduplication
//        long offset = value.getOffset();
//        String redisLastOffset = jedisCluster.getSet(StringUtils.join(value.getTopicAndPart(), groupId), Long.toString(offset));
//        if (redisLastOffset != null) {
//            long lastOffset = Long.parseLong(redisLastOffset);
//            if (lastOffset >= offset) {
//                duplicated++;
//                jedisCluster.getSet(StringUtils.join(value.getTopicAndPart(), groupId), redisLastOffset);
//                value.setIsProcessed(true);
//            }
//        }
        out.collect(value);
    }
}
