package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
@Slf4j
public class TD_B_PRODUCT_Mapper extends RichFlatMapFunction<Row, MajorBean> {
    private ParameterTool conf;
    private Long end_date_earliest;
    private SimpleDateFormat simpleDateFormat = null;
    private SimpleDateFormat simpleDateFormat2 = null;

    public TD_B_PRODUCT_Mapper(ParameterTool conf) {
        this.conf = conf;
        end_date_earliest = Long.parseLong(conf.get("table.end_date.ms", "1698854400000"));
        simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd:HH:mm:ss");
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        // PRODUCT_ID,ATTR_CODE,ATTR_VALUE,AREA_CODE,UPDATE_STAFF_ID,UPDATE_DEPART_ID,UPDATE_TIME,opt,optTime,cdhtime
        long currentTimeMillis = System.currentTimeMillis();
        MajorBean majorBean = new MajorBean();
        majorBean.setPRODUCT_ID(String.valueOf(value.getField("PRODUCT_ID")));
        majorBean.setPRODUCT_MODE(String.valueOf(value.getField("PRODUCT_MODE")));
        majorBean.setRSRV_VALUE2(String.valueOf(value.getField("RSRV_VALUE2")));
        String end_date = String.valueOf(value.getField("END_DATE"));
        long end_date_l = 0L;
        try {
            // date format conversion
            if (end_date.contains(":")) {
                if (end_date.contains(" ")) {
                    end_date_l = simpleDateFormat.parse(end_date).getTime();
                } else {
                    end_date_l = simpleDateFormat2.parse(end_date).getTime();
                }
            } else {
                end_date_l = Long.parseLong(end_date);
            }
            if (end_date_l < 1000000000000L) {
                throw new Exception("MY-ERROR-SubMapper: end_date < 1000000000000L Exception!");
            }
        } catch (Exception e) {
            log.error("MY-ERROR-SubMapper-parseError="+e);
            return;
        }
        majorBean.setEND_DATE(end_date);
        majorBean.setEnd_date_long(end_date_l);
        majorBean.setMyDataSource("TD_B_PRODUCT");
        majorBean.setRowKind(value.getKind());
        majorBean.setOpt(String.valueOf(value.getField("OPT")));
        majorBean.setOptTime(String.valueOf(value.getField("OPTTIME")));
//                majorBean.setCdhtime(String.valueOf(value.getField("cdhtime")));
        majorBean.setKafka_in_time((LocalDateTime) value.getField("KAFKA_TIME"));
//                majorBean.setKafka_out_time(String.valueOf(value.getField("kafka_out_time")));
//                majorBean.setPaimon_time(String.valueOf(value.getField("paimon_time")));
        majorBean.parseHeader2((Map<String, byte[]>) value.getField("HEADERS"));
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));
        out.collect(majorBean);
    }
}
