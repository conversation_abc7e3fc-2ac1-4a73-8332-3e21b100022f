package com.unicom.rts.integration.function;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/2/16 15:28
 * @Description
 */
public class Mapper3 extends RichFlatMapFunction<ConsumerRecord<String, String>, MajorBean> {
    private final static Logger logger = LoggerFactory.getLogger(Mapper3.class);

    private final ParameterTool conf;

    public Mapper3(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void flatMap(ConsumerRecord<String, String> busStr, Collector<MajorBean> out) throws Exception {
        try {
            String[] line = StringUtils.splitPreserveAllTokens(busStr.value(), "\u0001");
            if (44 != line.length) {
                logger.error("busStr Exception: busStr: {}", busStr);
                return;
            }
            String deviceNumber = line[BusEnum.DEVICE_NUMBER.ordinal()];
//            if (!provId.equals(line[39])) {
//                return;
//            }
            // 过滤掉非新入网用户
            if (!("9".equals(line[BusEnum.SUBSCRIBE_STATE.ordinal()]) && "0".equals(line[BusEnum.NEXT_DEAL_TAG.ordinal()]) && ("10".equals(line[BusEnum.TRADE_TYPE_CODE.ordinal()]) || "592".equals(line[BusEnum.TRADE_TYPE_CODE.ordinal()])))) {
                return;
            }
            // 号码长度限制
            if (deviceNumber.length() != 11) {
                return;
            }

            MajorBean MajorBean = new MajorBean();
            UserTagBean userTag = new UserTagBean();
            userTag.setDeviceNumber(line[BusEnum.DEVICE_NUMBER.ordinal()]);
            userTag.setUserIdBus(line[BusEnum.USER_ID.ordinal()]);
            userTag.setEparchyCodeBus(line[BusEnum.EPARCHY_CODE.ordinal()]);
            userTag.setProvIdBus(line[BusEnum.PROV_ID.ordinal()]);
            MajorBean.setUserTagBean(userTag);
            MajorBean.setSn(deviceNumber);
            MajorBean.setDataSource("bus");
            out.collect(MajorBean);
        } catch (Exception e) {
            logger.error("BusClean Exception:{} busStr:{}", e, busStr);
        }
    }
}
