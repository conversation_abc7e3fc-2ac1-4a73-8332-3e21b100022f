package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import com.unicom.rts.integration.bean.Operate;
import com.unicom.rts.integration.bean.SourceJsonBean;
import com.unicom.rts.integration.utils.MyHbaseUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TFUserProcessor implements Processor<MajorBean> {

	private final static Logger log = LoggerFactory.getLogger(TFUserProcessor.class);

	private MyHbaseUtil hbaseUtil;
	// private Connection connection;
	private HTable userTable;
	private String userTableRowkey;
	private String userTableColumnPrefix;
	private List<String> userTableColumnList;
	private HTable uidSnTable;
	private String uidSnTableRowkey;
	private static final byte[] FAMILYNAME = Bytes.toBytes("f");// 列族
	private String userTag;
	private String uidSn;
	// private ParameterTool config;
	private String user;
	private String zkHosts;
	private String zkNodeParent;
	private String zkPort;
	private final String dateFormat;
	private ValueState<List> userTagState;
	public TFUserProcessor(ParameterTool config) {
		//获取连接参数
		this.user = config.get("hbase.gch.user","hh_slfn2_sschj_gray");
		this.zkHosts = config.get("hbase.gch.zkHosts","**************,**************,**************");
		this.zkNodeParent = config.get("hbase.gch.zkNodeParent","/hbase-unsecure");
		this.zkPort = config.get("hbase.gch.zkPort","2181");
		this.dateFormat = config.get("rts_user_product.dateFormat");
		
		// 初始化hbase连接
		hbaseUtil = new MyHbaseUtil(user,zkHosts,zkNodeParent,zkPort);
		hbaseUtil.init();

		//获取表
		this.userTag = config.get("rts_user_tag","hh_slfn2_sschj_gray:user_m_tag");
		this.uidSn = config.get("rts_uid_sn","hh_slfn2_sschj_gray:rts_uid_sn_20230607");
		userTable = this.hbaseUtil.getTable(userTag);
		uidSnTable = this.hbaseUtil.getTable(uidSn);

		//获取rowkey
		userTableRowkey=config.get("rts_user_tag.rowkey","SERIAL_NUMBER");
//		userTableColumnPrefix=config.get("rts_user_tag.column.prefix","rt_b_");
		userTableColumnPrefix=config.get("rts_user_tag.column.prefix","");
		uidSnTableRowkey=config.get("rts_uid_sn.rowkey","USER_ID");

		//获取列
		String columns = config.get("ogg.user.columns");
//		userTableColumnList = Arrays.asList(StringUtils.splitPreserveAllTokens(columns, ","));
	}


	@Override
	public String process(MajorBean oggMessageBean) throws IllegalAccessException {

		log.debug("process............................");

		if ("Paimon".equalsIgnoreCase(oggMessageBean.getDataSource())) {

		}






//		Operate operate = oggMessageBean.getOperationType();
		String operate = oggMessageBean.getOperation();
		String result;

		switch (operate) {
			case "U":
				result = update(oggMessageBean);
				break;
			case "I":
				result = insert(oggMessageBean);
				break;
			case "D":
				result = delete(oggMessageBean);
//				result = "delete operation is not allowed!";
				break;
			case "K":
				result = key(oggMessageBean);
				break;
			default:
				result = "error operate, doing nothing!";
		}
		return result;
//		switch (operate) {
//			case Update:
//				result = update(oggMessageBean);
//				break;
//			case Insert:
//				result = insert(oggMessageBean);
//				break;
//			case Delete:
//				// result = delete(oggMessageBean);
//				result = "delete operation is not allowed!";
//				break;
//			case Key:
//				result = key(oggMessageBean);
//				break;
//			default:
//				result = "error operate, doing nothing!";
//		}
//		return result;
	}

	private String getSn(String userId){
		String sn=null;
		if(StringUtils.isEmpty(userId)){
			return null;
		}
		try {
			Result r = uidSnTable.get(new Get(Bytes.toBytes(userId)));
			if(null==r){
				return null;
			}
			byte[] valueByte = r.getValue(FAMILYNAME, Bytes.toBytes(uidSnTableRowkey));
			if(null==valueByte){
				return null;
			}
			sn=Bytes.toString(valueByte);
		} catch (IOException e) {
			log.error(e.toString());
		}
		return sn;	
	}

	//处理key类型的操作
	private String key(MajorBean oggMessageBean) throws IllegalAccessException {
		log.debug("key...");
		return "key:"+update(oggMessageBean);
	}

	//处理key类型的操作
	private String update(MajorBean oggMessageBean) throws IllegalAccessException {

		log.debug("update...");




//		Map<String,String> columns = new HashMap<String,String>();
		
//		oggMessageBean.getColumnList().forEach(column->{
//			log.debug("index->"+column.getIndex()+",columnName->"+column.getName()+",columnValue->"+column.getCurrentValue());
//			String columnName = column.getName();
//			//是否是需要的列
//			if(null==columnName || userTableColumnList.indexOf(columnName=columnName.toLowerCase())<0){
//				return;
//			}
//			//新值存在
//			if( column.isCurrentValueExist()){
//				//date转时间戳
//				if("in_date".equals(columnName)||"open_date".equals(columnName)||"develop_date".equals(columnName)){
//					// log.info("before parse -> "+column.currentValue);
//					// long dateMills = DateUtil.dateString2mill(column.getCurrentValue());
//					if (StringUtils.isNotBlank(column.getCurrentValue())) {
//						String dateFormated = DateUtil.dateStringFormat(column.getCurrentValue(), "yyyy-MM-dd HH:mm:ss");
//						// log.info("after parse -> "+dateMills);
//						columns.put(columnName, dateFormated);
//					}
//					// log.info("columnName value -> "+columns.get(columnName));
//				} else if ("province_code".equals(columnName)) {
//					if (column.getCurrentValue() != null) {
//						String provinceCode = column.getCurrentValue().trim().length() == 2 ? "0" + column.getCurrentValue() : column.getCurrentValue();
//						columns.put(columnName, provinceCode);
//						// log.info("province_code -> " + provinceCode);
//					}
//				} else{
//					columns.put(columnName, StringUtils.isEmpty(column.getCurrentValue()) ? "" : column.getCurrentValue());
//				}
//			}
//		});


/*		SourceJsonBean sourceJsonBean = oggMessageBean.getSourceJsonBean();
		Field[] declaredFields = sourceJsonBean.getClass().getDeclaredFields();
		for (Field field : declaredFields) {
			field.setAccessible(true);
			columns.put(field.getName(), StringUtils.isEmpty((CharSequence) field.get(sourceJsonBean)) ? "" : (String) field.get(sourceJsonBean));
		}

		// log.info("columns ------------> "+columns);

		//columns 为空，直接返回
		if(columns.size()<1){
			return "no columns update";
		}
		
		//user表
		String userTableRowkeyValue = columns.get(userTableRowkey);

		//serial_number为空
		if(StringUtils.isEmpty(userTableRowkeyValue)){
			//user_id为空
			if(null==columns.get(uidSnTableRowkey)){
				log.debug("no rows found");
				return "no columns update";
			}
			//user_id非空，则通过user_id获取serial_number
			userTableRowkeyValue = getSn(columns.get(uidSnTableRowkey));
		}

		//serial_number为空，则不处理
		if(StringUtils.isEmpty(userTableRowkeyValue)){
			log.debug("no rows found");
			return "no columns update";
		}

		byte[] rowKey = Bytes.toBytes(userTableRowkeyValue);
		//去掉userTableRowkey
		columns.remove(userTableRowkey);

		Put putUser = new Put(rowKey);
		columns.entrySet().iterator().forEachRemaining(entry->{
			//增加前缀
			byte[] key = Bytes.toBytes(userTableColumnPrefix+entry.getKey());
			byte[] value = null;
			if(null!=entry.getValue()){
				// log.info("key entry.getValue() -> "+entry.getValue());
				value = Bytes.toBytes(entry.getValue());
				// log.info("key value-> "+value);
			}
			putUser.addColumn(FAMILYNAME, key, value);
		});
		
		String result = "update:[";
		String uidSnTableRowkeyValue = columns.get(uidSnTableRowkey);
		Put putUidSn = null;
		if(StringUtils.isNotEmpty(uidSnTableRowkeyValue)){
			putUidSn = new Put(Bytes.toBytes(uidSnTableRowkeyValue));
			putUidSn.addColumn(FAMILYNAME, Bytes.toBytes(userTableRowkey), Bytes.toBytes(userTableRowkeyValue));
		}
		
		try {
			userTable.put(putUser);
			result=result+userTag+":"+putUser.toJSON(50);
			// log.info("update "+userTag+" by rowkey->"+userTableRowkeyValue+",result:"+result);
			if(null!=putUidSn){
				uidSnTable.put(putUidSn);
				result=result+","+uidSn+":"+putUidSn.toJSON();
				// log.info("update "+uidSn+" by rowkey->"+uidSnTableRowkeyValue+",result:"+result);
			}
		} catch (IOException e) {
			log.error("IOException:{}" , e);
		}
		result=result+"]";
		// log.info("put " + uidSnTableRowkeyValue + " >>>>>>>>>> " + result);
		
		return result;*/
		return "returnHere";
	}
	
	//处理key类型的操作
	private String insert(MajorBean oggMessageBean) throws IllegalAccessException {
		log.debug("insert...");
		return "insert:"+update(oggMessageBean);
	}
	
	//处理key类型的操作
	private String delete(MajorBean oggMessageBean){
		log.debug("delete...");

		Map<String,String> columns = new HashMap<String,String>();
		
//		oggMessageBean.getColumnList().forEach(column->{
//
//			log.debug("index->"+column.getIndex()+",columnName->"+column.getName()+",columnValue->"+column.getOldValue());
//
//			String columName = column.getName();
//			//是否是需要的列
//			if(null==columName || userTableColumnList.indexOf(columName)<0){
//				return;
//			}
//			//旧值存在
//			if( column.isOldValueExist()){
//				columns.put(columName, StringUtils.isEmpty(column.getOldValue())?"":column.getOldValue());
//			}
//		});
		
		//user表
		String userTableRowkeyValue = columns.get(userTableRowkey);
		if(StringUtils.isEmpty(userTableRowkeyValue)){
			log.debug("not record!");
			return "del: no rows deleted";
		}
		
		//去掉rowkey列
		columns.remove(userTableRowkey);
		
		Delete delUser = new Delete(Bytes.toBytes(userTableRowkeyValue));		
		columns.entrySet().iterator().forEachRemaining(entry->{
			delUser.addColumn(FAMILYNAME, Bytes.toBytes(entry.getKey()));
		});
		
		//uid_sn表
		String uidSnTableRowkeyValue = columns.get(uidSnTableRowkey);
		if(StringUtils.isEmpty(uidSnTableRowkeyValue)){
			log.debug("not record!");
			return "del: no rows deleted";
		}
		
		Delete delUidSn = null;
		if(StringUtils.isNotEmpty(uidSnTableRowkeyValue)){
			delUidSn = new Delete(Bytes.toBytes(uidSnTableRowkeyValue));
			delUidSn.addColumn(FAMILYNAME, Bytes.toBytes(userTableRowkey));
		}

		String result = "del:[";
		try {
			userTable.delete(delUser);
			result=result+userTag+":"+delUser.toJSON();
			log.debug("delete from "+userTag+" by rowkey->"+userTableRowkeyValue+",del:"+result);
			if(null!=delUidSn){
				uidSnTable.delete(delUidSn);
				result=result+","+uidSn+":"+delUidSn.toJSON();
				log.debug("delete form "+uidSn+" by rowkey->"+uidSnTableRowkeyValue+",del:"+result);
			}
		} catch (IOException e) {
			log.error("IOException:{}" , e);
		}
		result=result+"]";
		
		return result;
	}

}
