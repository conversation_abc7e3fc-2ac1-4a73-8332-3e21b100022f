package com.unicom.rts.integration.utils;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
public class MyFlinkSqlUtil {
    private final static DateTimeFormatter DATE_TIME_FORMATTER_YMD_1 = DateTimeFormatter.ofPattern("yyyyMMdd");
    private final static DateTimeFormatter DATE_TIME_FORMATTER_YMDHMS_1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static DateTimeFormatter DATE_TIME_FORMATTER_YMDHMS_2 = DateTimeFormatter.ofPattern("yyyy-MM-dd:HH:mm:ss");
    private final static DateTimeFormatter DATE_TIME_FORMATTER_YMD_2 = DateTimeFormatter.ISO_LOCAL_DATE; // yyyy-MM-dd
    private final static String[] REMOVE_TAG_ARRAY = {"0", "N", "1", "3"};
    private final static List<String> REMOVE_TAG_LIST = new ArrayList<>(Arrays.asList(REMOVE_TAG_ARRAY));
//    static {
//        sd.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
//    }

    public static List<String> getInsertSql_UNSUBSCRIBE_PRODUCT_IN(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String TableName_TF_F_UNSUBSCRIBE_PRODUCT = parameters.get("table.name.out", "dwa_r_paimon_unsubscribe_product_in_log, dwa_r_paimon_unsubscribe_product_in_pk");
        DataStream<Row> mainWriteStream = stream.flatMap(new FlatMapFunction<MajorBean, Row>() {
            @Override
            public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                String database_tag = valueIn.getDatabase_tag();
                String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                Set<String> productSet = valueIn.getProductSet();
                String[] productArray = new String[0];
                productArray = productSet.toArray(new String[0]);
                Arrays.sort(productArray);
                LocalDateTime now = LocalDateTime.now();
                String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                Row row = Row.withPositions(12);
                row.setField(0, system);
                row.setField(1, valueIn.getSn());
                row.setField(2, valueIn.getPROVINCE_CODE());
                row.setField(3, valueIn.getUserId());
                row.setField(4, productArray);
                row.setField(5, dateString);
                row.setField(6, java.time.LocalDateTime.now());
                row.setField(7, valueIn.getHeaders2());
                row.setField(8, valueIn.getMyDataSource());
                row.setField(9, valueIn.getOpt());
                row.setField(10, valueIn.getOptTime());
                row.setField(11, valueIn.getRowKind().toString());
                row.setKind(RowKind.INSERT);
                out.collect(row);
            }
        }).returns(Types.ROW_NAMED(
                new String[]{
                        "system",
                        "SERIAL_NUMBER",
                        "PROVINCE_CODE",
                        "USER_ID",
                        "UNSUBSCRIBE_PRODUCT_ID",
                        "process_out_date",
                        "paimon_time",
                        "headers",
                        "data_source",
                        "opt",
                        "opt_time",
                        "rowkind_ods"
                },
                Types.STRING,
                Types.STRING,
                Types.STRING,
                Types.STRING,
                Types.OBJECT_ARRAY(Types.STRING),
                Types.STRING,
                Types.LOCAL_DATE_TIME,
                Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                Types.STRING,
                Types.STRING,
                Types.STRING,
                Types.STRING
        )).uid("ToRowFlatMap").name("ToRowFlatMap").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("SERIAL_NUMBER", DataTypes.STRING())
                .column("PROVINCE_CODE", DataTypes.STRING())
                .column("USER_ID", DataTypes.STRING())
                .column("UNSUBSCRIBE_PRODUCT_ID", DataTypes.ARRAY(DataTypes.STRING()))
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        String[] tableNameSplit = TableName_TF_F_UNSUBSCRIBE_PRODUCT.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }

        return returnlist;
    }

    public static List<String> getInsertSql_SP_PRODUCT_IN(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String TableName_TF_F_USER_SP = parameters.get("table.name.out", "dwa_r_paimon_sp_product_in_log,dwa_r_paimon_sp_product_in_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        Set<String> productSet = valueIn.getProductSet();
                        String[] productArray = new String[0];
                        if (REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG())) {
                            productArray = productSet.toArray(new String[0]);
                            Arrays.sort(productArray);
                        }
                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        Row row = Row.withPositions(12);
                        row.setField(0, system);
                        row.setField(1, valueIn.getSn());
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, productArray);
                        row.setField(5, dateString);
                        row.setField(6, now);
                        row.setField(7, valueIn.getHeaders2());
                        row.setField(8, valueIn.getMyDataSource());
                        row.setField(9, valueIn.getOpt());
                        row.setField(10, valueIn.getOptTime());
                        row.setField(11, valueIn.getRowKind().toString());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "SP_PRODUCT_IN",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                ))
                .uid("ToRowFlatMap").name("ToRowFlatMap").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("SP_PRODUCT_IN", DataTypes.ARRAY(DataTypes.STRING()))
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        String[] tableNameSplit = TableName_TF_F_USER_SP.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }

        return returnlist;
    }

    public static List<String> getInsertSql_R_SERVICE_IN(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_r_service_in_log,dwa_r_paimon_r_service_in_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        boolean isRateLimit = false;
                        Set<String> productSet = valueIn.getProductSet();
                        String[] productArray = new String[0];
                        if (REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG())) {
                            if (productSet.contains("50027") || productSet.contains("50033")) {
                                isRateLimit = true;
                            }
                            productArray = productSet.toArray(new String[0]);
                            Arrays.sort(productArray);
                        }
                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        Row row = Row.withPositions(14);
                        row.setField(0, system);
                        row.setField(1, valueIn.getSn());
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, productArray);
                        row.setField(5, isRateLimit);
                        row.setField(6, dateString);
                        row.setField(7, now);
                        row.setField(8, valueIn.getHeaders2());
                        row.setField(9, valueIn.getMyDataSource());
                        row.setField(10, valueIn.getOpt());
                        row.setField(11, valueIn.getOptTime());
                        row.setField(12, valueIn.getRowKind().toString());
                        row.setField(13, valueIn.getSERVICE_IS_MONTH_RATE_LIMIT());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "R_SERVICE_IN",
                                "SERVICE_IS_RATE_LIMIT",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods",
                                "SERVICE_IS_MONTH_RATE_LIMIT"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.BOOLEAN
                ))
                .uid("ToRowFlatMap").name("ToRowFlatMap").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("R_SERVICE_IN", DataTypes.ARRAY(DataTypes.STRING()))
                .column("SERVICE_IS_RATE_LIMIT", DataTypes.BOOLEAN())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .column("SERVICE_IS_MONTH_RATE_LIMIT", DataTypes.BOOLEAN())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        String[] tableNameSplit = tableNameOut.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        return returnlist;
    }

    public static List<String> getInsertSql_R_DISCNT_IN(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_r_discnt_in_log,dwa_r_paimon_r_discnt_in_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        Set<String> productSet = valueIn.getProductSet();
                        String[] productArray = new String[0];
                        if (REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG())) {
                            productArray = productSet.toArray(new String[0]);
                            Arrays.sort(productArray);
                        }
                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        Row row = Row.withPositions(12);
                        row.setField(0, system);
                        row.setField(1, valueIn.getSn());
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, productArray);
                        row.setField(5, dateString);
                        row.setField(6, now);
                        row.setField(7, valueIn.getHeaders2());
                        row.setField(8, valueIn.getMyDataSource());
                        row.setField(9, valueIn.getOpt());
                        row.setField(10, valueIn.getOptTime());
                        row.setField(11, valueIn.getRowKind().toString());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "R_DISCNT_IN",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                ))
                .uid("ToRowFlatMap").name("ToRowFlatMap").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("R_DISCNT_IN", DataTypes.ARRAY(DataTypes.STRING()))
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        String[] tableNameSplit = tableNameOut.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        return returnlist;
    }

    public static List<String> getInsertSql_USER_IS_MIXED(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_user_is_mixed_log,dwa_r_paimon_user_is_mixed_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        Set<String> productSet = valueIn.getProductSet();
                        boolean user_is_mixd = false;
                        if (REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG())) {
                            user_is_mixd = !productSet.isEmpty();
                        }

                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        Row row = Row.withPositions(14);
                        row.setField(0, system);
                        row.setField(1, valueIn.getSn());
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, user_is_mixd);
                        row.setField(5, dateString);
                        row.setField(6, now);
                        row.setField(7, valueIn.getHeaders2());
                        row.setField(8, valueIn.getMyDataSource());
                        row.setField(9, valueIn.getOpt());
                        row.setField(10, valueIn.getOptTime());
                        row.setField(11, valueIn.getRowKind().toString());
                        row.setField(12, valueIn.getUSER_IS_ZF());
                        row.setField(13, valueIn.getUSER_ZHWJ_SHARES_ZF());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "USER_IS_MIXED",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods",
                                "USER_IS_ZF",
                                "USER_ZHWJ_SHARES_ZF"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                ))
                .uid("ToRowFlatMap").name("ToRowFlatMap").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("USER_IS_MIXED", DataTypes.BOOLEAN())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .column("USER_IS_ZF", DataTypes.STRING())
                .column("USER_ZHWJ_SHARES_ZF", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        String[] tableNameSplit = tableNameOut.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        return returnlist;
    }

    public static List<String> getInsertSql_USER_IN(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_user_in_log,dwa_r_paimon_user_in_pk");
        String tableNameOut2 = parameters.get("table.name.out2", "dwa_r_paimon_prov_eparchy_log,dwa_r_paimon_prov_eparchy_pk");
        String tableNameOut3 = parameters.get("table.name.out3", "dwa_r_paimon_is_arrearage_log,dwa_r_paimon_is_arrearage_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33,30,40,60,70,90").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        String[] netTypeCodeArray2 = parameters.get("table.netTypeCode2", "50,33,40,60,70,90").split(",", -1);
        Boolean isAny2 = "Any".equalsIgnoreCase(netTypeCodeArray2[0]);
        List<String> netTypeCodeList2 = new ArrayList<>(Arrays.asList(netTypeCodeArray2));
        String[] netTypeCodeArray3 = parameters.get("table.netTypeCode3", "50,33,30,40,60,70,90").split(",", -1);
        List<String> netTypeCodeList3 = new ArrayList<>(Arrays.asList(netTypeCodeArray3));
        // USER_IN
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if ("TF_O_LEAVEREALFEE".equals(valueIn.getMyDataSource())) {
                            return;
                        }
                        if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        Set<String> productSet = valueIn.getProductSet();
                        String[] productArray = productSet.toArray(new String[0]);
                        Arrays.sort(productArray);
                        String netTypeCodeNew = null;
                        switch (valueIn.getNET_TYPE_CODE()) {
                            case "33":
                                netTypeCodeNew = "1";
                                break;
                            case "50":
                                String brand_code = valueIn.getBRAND_CODE();
                                if ("5G00".equals(brand_code)) {
                                    netTypeCodeNew = "10";
//                                } else if ("4G00".equals(brand_code) || "4G01".equals(brand_code)) {
                                } else {
                                    netTypeCodeNew = "2";
                                }
                                break;
                            case "30":
                                netTypeCodeNew = "4";
                                break;
                            case "40":
                            case "60":
                            case "70":
                                netTypeCodeNew = "3";
                                break;
                            case "CP":
                                netTypeCodeNew = "5";
                                break;
                            case "WV":
                                netTypeCodeNew = "6";
                                break;
                            case "XN":
                                netTypeCodeNew = "7";
                                break;
                            case "90":
                                netTypeCodeNew = "11";
                                break;
                            default:
                        }

                        String open_date = valueIn.getOPEN_DATE();
                        int totalMonthDiff = 0;
                        double leaveRealFeeD = 0D;
                        String leave_real_fee = valueIn.getLEAVE_REAL_FEE();
                        boolean leave_real_fee_isNull = StringUtils.isBlank(leave_real_fee);
                        int credit_value = 0;
                        try {
                            credit_value = (int) Double.parseDouble(valueIn.getCREDIT_VALUE());
                            // date format conversion
                            LocalDateTime dateOpen;
                            if (open_date.contains(":")) {
                                if (open_date.contains(" ")) {
                                    dateOpen = LocalDateTime.parse(open_date, DATE_TIME_FORMATTER_YMDHMS_1);
                                } else {
                                    dateOpen = LocalDateTime.parse(open_date, DATE_TIME_FORMATTER_YMDHMS_2);
                                }
                            } else {
                                dateOpen = LocalDateTime.ofEpochSecond(Long.parseLong(open_date) / 1000, 0, ZoneOffset.ofHours(8));
                            }
                            LocalDate dateNow = LocalDate.now();
                            Period period = Period.between(dateOpen.toLocalDate(), dateNow);
                            int yearDiff = period.getYears();
                            int monthDiff = period.getMonths();
                            totalMonthDiff = yearDiff * 12 + monthDiff;
//                            if (totalMonthDiff > 1200) {
//                                throw new Exception("MY-ERROR: open_date < 1000000000000L Exception!");
//                            }
                            if (!leave_real_fee_isNull) {
                                leaveRealFeeD = Double.parseDouble(leave_real_fee);
                            }
                        } catch (Exception e) {
                            log.error("MY-ERROR-ToRowFlatMap-parseError=" + e);
                            return;
                        }

                        Boolean user_is_use = REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG());

                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        Row row = Row.withPositions(28);
                        row.setField(0, system);
                        row.setField(1, valueIn.getSn());
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, totalMonthDiff);
                        row.setField(5, valueIn.getPRODUCT_ID());
                        row.setField(6, "0".equals(valueIn.getUSER_STATE_CODESET()));
                        row.setField(7, credit_value);
                        row.setField(8, valueIn.getUSER_STATE_CODESET());
                        row.setField(9, valueIn.getPROVINCE_CODE());
                        row.setField(10, system);
                        row.setField(11, totalMonthDiff);
                        row.setField(12, valueIn.getPRODUCT_ID());
                        row.setField(13, "0".equals(valueIn.getUSER_STATE_CODESET()));
                        row.setField(14, netTypeCodeNew);
                        row.setField(15, user_is_use);
                        row.setField(16, productArray);
                        if (leave_real_fee_isNull) {
                            row.setField(17, false);
                        } else {
                            row.setField(17, leaveRealFeeD < 0);
                        }
                        row.setField(18, leave_real_fee);
                        row.setField(19, valueIn.getEPARCHY_CODE());
                        row.setField(20, valueIn.getOPEN_DATE());
                        row.setField(21, dateString);
                        row.setField(22, now);
                        row.setField(23, valueIn.getHeaders2());
                        row.setField(24, valueIn.getMyDataSource());
                        row.setField(25, valueIn.getOpt());
                        row.setField(26, valueIn.getOptTime());
                        row.setField(27, valueIn.getRowKind().toString());
                 //       row.setField(26, valueIn.getManagerId());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "R_USER_NETMONTH",
                                "R_PRODUCT_MAIN",
                                "R_USER_IS_SERVICE_OPEN",
                                "CREDIT_LIMIT",
                                "USER_STATE_CODESET",
                                "USER_PROVINCE_CODE",
                                "USER_SYSTEM_CODE",
                                "USER_NETMONTH",
                                "PRODUCT_MAIN",
                                "USER_IS_SERVICE_OPEN",
                                "NET_TYPE_CODE",
                                "USER_IS_USE",
                                "ZDC_WAY",
                                "IS_ARREARAGE",
                                "LEAVE_REAL_FEE",
                                "EPARCHY_CODE",
                                "OPEN_DATE",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
                            //    "CUST_MANAGER_ID"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.INT,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.INT,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.INT,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                      //  Types.STRING
                ))
                .uid("ToRowFlatMap1").name("ToRowFlatMap1").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("R_USER_NETMONTH", DataTypes.INT())
                .column("R_PRODUCT_MAIN", DataTypes.STRING())
                .column("R_USER_IS_SERVICE_OPEN", DataTypes.BOOLEAN())
                .column("CREDIT_LIMIT", DataTypes.INT())
                .column("USER_STATE_CODESET", DataTypes.STRING())
                .column("USER_PROVINCE_CODE", DataTypes.STRING())
                .column("USER_SYSTEM_CODE", DataTypes.STRING())
                .column("USER_NETMONTH", DataTypes.INT())
                .column("PRODUCT_MAIN", DataTypes.STRING())
                .column("USER_IS_SERVICE_OPEN", DataTypes.BOOLEAN())
                .column("NET_TYPE_CODE", DataTypes.STRING())
                .column("USER_IS_USE", DataTypes.BOOLEAN())
                .column("ZDC_WAY", DataTypes.ARRAY(DataTypes.STRING()))
                .column("IS_ARREARAGE", DataTypes.BOOLEAN())
                .column("LEAVE_REAL_FEE", DataTypes.STRING())
                .column("EPARCHY_CODE", DataTypes.STRING())
                .column("OPEN_DATE", DataTypes.STRING())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                //.column("CUST_MANAGER_ID", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        // PROV_EPARCHY
        DataStream<Row> mainWriteStream2 = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!"TF_F_USER".equals(valueIn.getMyDataSource())) {
                            return;
                        }
                        if (!isAny2 && !netTypeCodeList2.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        Boolean user_is_use = REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG());
                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        String netTypeCodeNew = null;
                        switch (valueIn.getNET_TYPE_CODE()) {
                            case "33":
                                netTypeCodeNew = "1";
                                break;
                            case "50":
                                String brand_code = valueIn.getBRAND_CODE();
                                if ("5G00".equals(brand_code)) {
                                    netTypeCodeNew = "10";
//                                } else if ("4G00".equals(brand_code) || "4G01".equals(brand_code)) {
                                } else {
                                    netTypeCodeNew = "2";
                                }
                                break;
                            case "40":
                            case "60":
                            case "70":
                                netTypeCodeNew = "3";
                                break;
                            case "90":
                                netTypeCodeNew = "11";
                                break;
                            default:
                        }

                        if ("10".equals(netTypeCodeNew)) {
                            return;
                        }

                        Row row = Row.withPositions(14);
                        row.setField(0, system);
                        row.setField(1, valueIn.getSn());
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, valueIn.getEPARCHY_CODE());
                        row.setField(5, netTypeCodeNew);
                        row.setField(6, user_is_use);
                        row.setField(7, dateString);
                        row.setField(8, now);
                        row.setField(9, valueIn.getHeaders2());
                        row.setField(10, valueIn.getMyDataSource());
                        row.setField(11, valueIn.getOpt());
                        row.setField(12, valueIn.getOptTime());
                        row.setField(13, valueIn.getRowKind().toString());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "EPARCHY_CODE",
                                "NET_TYPE_CODE",
                                "USER_IS_USE",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                ))
                .uid("ToRowFlatMap2").name("ToRowFlatMap2").setParallelism(parameters.getInt("mapper.torow2.parallelism", 1));

        Schema schema2 = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("EPARCHY_CODE", DataTypes.STRING())
                .column("NET_TYPE_CODE", DataTypes.STRING())
                .column("USER_IS_USE", DataTypes.BOOLEAN())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table2 = tableEnv.fromChangelogStream(mainWriteStream2, schema2);

        tableEnv.createTemporaryView("TemporaryView2", table2);

        // IS_ARREARAGE
        DataStream<Row> mainWriteStream3 = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!"TF_O_LEAVEREALFEE".equals(valueIn.getMyDataSource()) &&
                                !"TF_F_USER".equals(valueIn.getMyDataSource())
                        ) {
                            return;
                        }
                        if (!netTypeCodeList3.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
//                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        String system = "9900_2i";
                        double leaveRealFeeD = 0D;
                        String leave_real_fee = valueIn.getLEAVE_REAL_FEE();
                        try {
                            leaveRealFeeD = Double.parseDouble(leave_real_fee);
                        } catch (Exception e) {
                            log.error("MY-ERROR-ToRowFlatMap-parseError=" + e+leave_real_fee);
                            return;
                        }

                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        Row row = Row.withPositions(13);
                        row.setField(0, valueIn.getPROVINCE_CODE());
                        row.setField(1, valueIn.getUserId());
                        row.setField(2, leaveRealFeeD < 0);
                        row.setField(3, valueIn.getSn());
                        row.setField(4, system);
                        row.setField(5, leave_real_fee);
                        row.setField(6, dateString);
                        row.setField(7, now);
                        row.setField(8, valueIn.getHeaders2());
                        row.setField(9, valueIn.getMyDataSource());
                        row.setField(10, valueIn.getOpt());
                        row.setField(11, valueIn.getOptTime());
                        row.setField(12, valueIn.getRowKind().toString());
//                        row.setField(13, valueIn.getManagerId());
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "province_code",
                                "user_id",
                                "IS_ARREARAGE",
                                "serial_number",
                                "system",
                                "LEAVE_REAL_FEE",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
//                                "CUST_MANAGER_ID"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
//                        Types.STRING
                ))
                .uid("ToRowFlatMap3").name("ToRowFlatMap3").setParallelism(parameters.getInt("mapper.torow3.parallelism", 1));

        Schema schema3 = Schema.newBuilder()
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("IS_ARREARAGE", DataTypes.BOOLEAN())
                .column("serial_number", DataTypes.STRING())
                .column("system", DataTypes.STRING())
                .column("LEAVE_REAL_FEE", DataTypes.STRING())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
//                .column("CUST_MANAGER_ID", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table3 = tableEnv.fromChangelogStream(mainWriteStream3, schema3);

        tableEnv.createTemporaryView("TemporaryView3", table3);

        List<String> returnlist = new ArrayList<>();
        String[] tableNameSplit = tableNameOut.split(",", -1);
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        String[] tableNameSplit2 = tableNameOut2.split(",", -1);
        for (String s : tableNameSplit2) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView2");
        }
        String[] tableNameSplit3 = tableNameOut3.split(",", -1);
        for (String s : tableNameSplit3) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView3");
        }
        return returnlist;
    }

    public static List<String> getInsertSql_CMW_CUST_MANAGER_REF(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_cmw_cust_manager_ref_log,dwa_r_paimon_cmw_cust_manager_ref_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33,30,40,60,70,90").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        // USER_IN
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap((FlatMapFunction<MajorBean, Row>) (valueIn, out) -> {
                    if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                        return;
                    }
                    String database_tag = valueIn.getDatabase_tag();
                    String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";

                    LocalDateTime now = LocalDateTime.now();
                    String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                    Row row = Row.withPositions(12);
                    row.setField(0, system);
                    row.setField(1, valueIn.getSn());
                    row.setField(2, valueIn.getPROVINCE_CODE());
                    row.setField(3, valueIn.getUserId());
                    row.setField(4, valueIn.getManagerId());
                    row.setField(5, dateString);
                    row.setField(6, now);
                    row.setField(7, valueIn.getHeaders2());
                    row.setField(8, valueIn.getMyDataSource());
                    row.setField(9, valueIn.getOpt());
                    row.setField(10, valueIn.getOptTime());
                    row.setField(11, valueIn.getRowKind().toString());
                    row.setKind(RowKind.INSERT);
                    out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "CUST_MANAGER_ID",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                        //  Types.STRING
                ))
                .uid("ToRowFlatMap1").name("ToRowFlatMap1").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("CUST_MANAGER_ID", DataTypes.STRING())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                //.column("CUST_MANAGER_ID", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        List<String> returnlist = new ArrayList<>();
        String[] tableNameSplit = tableNameOut.split(",", -1);
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        return returnlist;
    }

    public static List<String> getInsertSql_PRODUCT_IN(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_product_in_log,dwa_r_paimon_product_in_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33,40,60,70,90").split(",", -1);
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                            return;
                        }
                        String database_tag = valueIn.getDatabase_tag();
                        String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";
                        List<String> combinedProductInfoList = valueIn.getProductList();
                        List<String> productSet = new ArrayList<>();
                        List<String> productModeList = new ArrayList<>();
                        List<String> endDateList = new ArrayList<>();

                        for (String combinedInfo : combinedProductInfoList) {
                            String[] parts = combinedInfo.split("##_##", -1);
                            if (parts.length == 3) {
                                productSet.add(parts[0]);
                                productModeList.add(parts[1]);
                                endDateList.add(parts[2]);
                            }
                        }

                        String[] productArray = new String[0];
                        String[] productModeArray = new String[0];
                        String[] endDateArray = new String[0];
                        boolean remove_tag_in = REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG());
                        if (remove_tag_in) {
                            productArray = productSet.toArray(new String[0]);
                            productModeArray = productModeList.toArray(new String[0]);
                            endDateArray = endDateList.toArray(new String[0]);
                        }
                        LocalDateTime now = LocalDateTime.now();
                        String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                        // --- 新增CONTRACT_ID逻辑 ---
                        List<String> contractProductIds = new ArrayList<>();
                        Map<String, Integer> productIdCount = new HashMap<>(); // 统计每个产品ID的出现次数
                        LocalDate nextMonthFirstDay = LocalDate.now().plusMonths(1).withDayOfMonth(1);
                        if (remove_tag_in) {
                            // 第一遍：统计PRODUCT_MODE=50且END_DATE<次月第一天的产品ID出现次数
                            for (int i = 0; i < productModeList.size(); i++) {
                                try {
                                    if ("50".equals(productModeList.get(i))) {
                                        String productId = productSet.get(i);
                                        String endDateStr = endDateList.get(i);
                                        LocalDateTime endDate = null;
                                        // 尝试两种日期格式
                                        try {
                                            endDate = LocalDateTime.parse(endDateStr, DATE_TIME_FORMATTER_YMDHMS_1);
                                        } catch (Exception e1) {
                                            try {
                                                endDate = LocalDateTime.parse(endDateStr, DATE_TIME_FORMATTER_YMDHMS_2);
                                            } catch (Exception e2) {
                                                throw new Exception("Both date formats failed");
                                            }
                                        }
                                        // 条件：END_DATE < 次月第一天
                                        if (endDate.toLocalDate().isBefore(nextMonthFirstDay)) {
                                            productIdCount.put(productId, productIdCount.getOrDefault(productId, 0) + 1);
                                        }
                                    }
                                } catch (Exception e) {
                                    // 日期格式错误或其他异常，跳过
                                    log.warn("MY-WARN-ParseEndDateError, userId=" + valueIn.getUserId() + ", productId=" + productSet.get(i) + ", endDate=" + endDateList.get(i));
                                }
                            }
                            // 第二遍：只添加出现次数为1的产品ID
                            for (Map.Entry<String, Integer> entry : productIdCount.entrySet()) {
                                if (entry.getValue() == 1) {
                                    contractProductIds.add(entry.getKey());
                                }
                            }
                        }
                        String contractId = String.join("|", contractProductIds);
                        // --- 逻辑结束 ---

                        Row row = Row.withPositions(21);
                        row.setField(0, productArray);
                        row.setField(1, remove_tag_in ? valueIn.getValid_main_product() : "");
                        row.setField(2, valueIn.getPROVINCE_CODE());
                        row.setField(3, valueIn.getUserId());
                        row.setField(4, valueIn.getSn());
                        row.setField(5, system);
                        row.setField(6, remove_tag_in ? valueIn.getProduct_is_2i2c() : false);
                        row.setField(7, productArray);
                        row.setField(8, remove_tag_in ? valueIn.getProduct_is_2i2c() : false);
                        row.setField(9, remove_tag_in ? valueIn.getIs_contract_user() : false);
                        row.setField(10, dateString);
                        row.setField(11, now);
                        row.setField(12, valueIn.getHeaders2());
                        row.setField(13, valueIn.getMyDataSource());
                        row.setField(14, valueIn.getOpt());
                        row.setField(15, valueIn.getOptTime());
                        row.setField(16, valueIn.getRowKind().toString());
                        row.setField(17, StringUtils.isBlank(valueIn.getMAIN_PRODUCT_MONTHLY_FEE())? "":valueIn.getMAIN_PRODUCT_MONTHLY_FEE());
                        row.setField(18, productModeArray);
                        row.setField(19, endDateArray);
                        row.setField(20, contractId); // 设置CONTRACT_ID
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
//                        log.info("MY-INFO-ToRowFlatMap-out="+row.toString());
                    }
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "PRODUCT_IN",
                                "VALID_MAIN_PRODUCT",
                                "province_code",
                                "user_id",
                                "serial_number",
                                "system",
                                "PRODUCT_IS_2I2C",
                                "R_PRODUCT_IN",
                                "R_PRODUCT_IS_2I2C",
                                "IS_CONTRACT_USER",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods",
                                "MAIN_PRODUCT_MONTHLY_FEE",
                                "PRODUCT_MODE",
                                "PRODUCT_END_DATE",
                                "CONTRACT_ID"
                        },
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.BOOLEAN,
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.OBJECT_ARRAY(Types.STRING),
                        Types.STRING
                ))
                .uid("ToRowFlatMap").name("ToRowFlatMap").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("PRODUCT_IN", DataTypes.ARRAY(DataTypes.STRING()))
                .column("VALID_MAIN_PRODUCT", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("system", DataTypes.STRING())
                .column("PRODUCT_IS_2I2C", DataTypes.BOOLEAN())
                .column("R_PRODUCT_IN", DataTypes.ARRAY(DataTypes.STRING()))
                .column("R_PRODUCT_IS_2I2C", DataTypes.BOOLEAN())
                .column("IS_CONTRACT_USER", DataTypes.BOOLEAN())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .column("MAIN_PRODUCT_MONTHLY_FEE", DataTypes.STRING())
                .column("PRODUCT_MODE", DataTypes.ARRAY(DataTypes.STRING()))
                .column("PRODUCT_END_DATE", DataTypes.ARRAY(DataTypes.STRING()))
                .column("CONTRACT_ID", DataTypes.STRING())
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("TemporaryView", table);

        String[] tableNameSplit = tableNameOut.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        return returnlist;
    }


    public static List<String> getInsertSql_HUAFEIQUAN_TAG(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_prod_flink");
        String tableNameOut = parameters.get("table.name.out", "dwa_r_paimon_huafeiquan_log,dwa_r_paimon_huafeiquan_pk");
        String[] netTypeCodeArray = parameters.get("table.netTypeCode", "50,33,30,40,60,70,90").split(",", -1);
        String loginInfoTable = parameters.get("table.name.login_info", "ods_r_paimon_st_login_info");
        List<String> netTypeCodeList = new ArrayList<>(Arrays.asList(netTypeCodeArray));
        // USER_IN
        DataStream<Row> mainWriteStream = stream
                .keyBy(MajorBean::getUserId)
                .flatMap((FlatMapFunction<MajorBean, Row>) (valueIn, out) -> {
                    if (!netTypeCodeList.contains(valueIn.getNET_TYPE_CODE())) {
                        return;
                    }
                    if (!"TF_F_USER".equals(valueIn.getMyDataSource())) {
                        return;
                    }
                    if (!REMOVE_TAG_LIST.contains(valueIn.getREMOVE_TAG())) {
                        return;
                    }

                    String database_tag = valueIn.getDatabase_tag();
                    String system = Objects.equals(database_tag, "default") || Objects.equals(database_tag, "Drds") ? "9900_2i" : "9900";

                    LocalDateTime now = LocalDateTime.now();
                    String dateString = DATE_TIME_FORMATTER_YMD_1.format(now); //将时间格式化为字符串

                    Row row = Row.withPositions(12);
                    row.setField(0, system);
                    row.setField(1, valueIn.getSn());
                    row.setField(2, valueIn.getPROVINCE_CODE());
                    row.setField(3, valueIn.getUserId());
                    row.setField(4, false); // ACTIVATE_FLAG 默认为false，后续通过SQL关联更新
                    row.setField(5, dateString);
                    row.setField(6, now);
                    row.setField(7, valueIn.getHeaders2());
                    row.setField(8, valueIn.getMyDataSource());
                    row.setField(9, valueIn.getOpt());
                    row.setField(10, valueIn.getOptTime());
                    row.setField(11, valueIn.getRowKind().toString());
                    row.setKind(RowKind.INSERT);
                    out.collect(row);
                })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "serial_number",
                                "province_code",
                                "user_id",
                                "ACTIVATE_FLAG",
                                "process_out_date",
                                "paimon_time",
                                "headers",
                                "data_source",
                                "opt",
                                "opt_time",
                                "rowkind_ods"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.BOOLEAN,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.MAP(Types.STRING, Types.PRIMITIVE_ARRAY(Types.BYTE)),
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                ))
                .uid("ToRowFlatMapHuafeiquan").name("ToRowFlatMapHuafeiquan").setParallelism(parameters.getInt("mapper.torow.parallelism", 1));

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("serial_number", DataTypes.STRING())
                .column("province_code", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("ACTIVATE_FLAG", DataTypes.BOOLEAN())
                .column("process_out_date", DataTypes.STRING())
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .column("headers", DataTypes.MAP(DataTypes.STRING(), DataTypes.BYTES()))
                .column("data_source", DataTypes.STRING())
                .column("opt", DataTypes.STRING())
                .column("opt_time", DataTypes.STRING())
                .column("rowkind_ods", DataTypes.STRING())
                .build();

        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);
        tableEnv.createTemporaryView("TempUserView", table);

        // 通过SQL实现关联逻辑，更新ACTIVATE_FLAG
        String sqlQuery = String.format(
                "SELECT " +
                        "    t.system, " +
                        "    t.serial_number, " +
                        "    t.province_code, " +
                        "    t.user_id, " +
                        "    CASE WHEN l.serial_number IS NOT NULL THEN TRUE ELSE FALSE END AS ACTIVATE_FLAG, " +
                        "    t.process_out_date, " +
                        "    t.paimon_time, " +
                        "    t.headers, " +
                        "    t.data_source, " +
                        "    t.opt, " +
                        "    t.opt_time, " +
                        "    t.rowkind_ods " +
                        "FROM TempUserView t " +
                        "LEFT JOIN `paimon_catalog`.`%s`.`%s` l ON t.serial_number = l.serial_number",
                defaultDatabase, loginInfoTable);

        Table resultTable = tableEnv.sqlQuery(sqlQuery);
        tableEnv.createTemporaryView("TemporaryView", resultTable);

        List<String> returnlist = new ArrayList<>();
        String[] tableNameSplit = tableNameOut.split(",", -1);
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM TemporaryView");
        }
        return returnlist;
    }


    // BAK-
    public static List<String> getInsertSql_TF_F_USER_SP(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_gray_flink");
        String TableName_TF_F_USER_SP = parameters.get("table.name.TF_F_USER_SP", "ods_r_paimon_tf_f_user_sp_tag_fc_20230706,ods_r_paimon_tf_f_user_sp_tag_lookup_20230706,ods_r_paimon_tf_f_user_sp_tag_append_20230706");
        DataStream<Row> mainWriteStream = stream.flatMap(new FlatMapFunction<MajorBean, Row>() {
            @Override
            public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                String opt = valueIn.getOperation();
                Long timeStamp = valueIn.getTimeStamp();
                long second = timeStamp / 1000;
                int millisecond = (int) (timeStamp % 1000);

                Row row = Row.withPositions(10);
                row.setField(0, valueIn.getSystem());
                row.setField(1, valueIn.getTable_name());
                row.setField(2, valueIn.getOperation());
                row.setField(3, valueIn.getUSER_ID());
                row.setField(4, valueIn.getSP_PRODUCT_ID());
                row.setField(5, valueIn.getSTART_DATE());
                row.setField(6, valueIn.getEND_DATE());
                row.setField(7, java.time.LocalDateTime.ofEpochSecond(second, millisecond * 1000000, ZoneOffset.ofHours(8)));
                row.setField(8, java.time.LocalDateTime.ofEpochSecond(second, millisecond * 1000000, ZoneOffset.ofHours(8)));
                row.setField(9, java.time.LocalDateTime.now());
//                if ("D".equals(opt)) {
//                    System.out.println(row);
//                    row.setKind(RowKind.DELETE);
//                    System.out.println("-->" + row);
//                }
                switch (opt) {
                    case "I":
                    case "U":
//                        row.setKind(RowKind.UPDATE_AFTER);
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
                        break;
                    case "D":
                        row.setKind(RowKind.DELETE);
                        out.collect(row);
                        break;
                    default:
                }
                        /*
                        event_time         数据发生时间，即事件时间戳（13位，毫秒）注：如无此值，无须记入header中
                            |
                            v
                        receive_time       数据接收时间，即上游kafka消息的时间戳（13位，毫秒）
                            |
                            v
                        integration_time   整合层开始处理时间戳（13位，毫秒）注：两级架构无此值，无须记入header中
                            |
                            v
                        root_time          数据加工接收时间，即根kafka消息的时间戳（13位，毫秒）注：两级架构无此值，无须记入header中
                            |
                            v
                        process_time       数据加工层开始处理时间戳（13位，毫秒）
                            |
                            v
                        release_time       数据下发时间，即下游（对外）kafka消息的时间戳（13位，毫秒）

                        scene_id           场景ID（通过配置化页面新增场景）
                        event_province     用户事件发生省（非必填）注：如无此值，无须记入header中
                        owner_province     归属省（非必填）注：如无此值，无须记入header中
                        */

//                String eventTime = MyUtil.getHeader(messageBean.getHeaders(), "event_time");
//                String receiveime = MyUtil.getHeader(messageBean.getHeaders(), "receive_time");
//                String integrationTime = MyUtil.getHeader(messageBean.getHeaders(), "integration_time");
//                String rootTime = MyUtil.getHeader(messageBean.getHeaders(), "root_time");
//                String processTime = MyUtil.getHeader(messageBean.getHeaders(), "process_time");
//                String releaseTime = MyUtil.getHeader(messageBean.getHeaders(), "release_time");
//                String sceneId = MyUtil.getHeader(messageBean.getHeaders(), "scene_id");
//                String eventProvince = MyUtil.getHeader(messageBean.getHeaders(), "event_province");
//                String ownerProvince = MyUtil.getHeader(messageBean.getHeaders(), "owner_province");
//
//                String dateId = sd.format(messageBean.getConsumerRecordTimestamp());

//                return Row.ofKind(RowKind.INSERT,
//                        connId,
//                        dateId,
//                        eventTime,
//                        receiveime,
//                        integrationTime,
//                        rootTime,
//                        processTime,
//                        releaseTime,
//                        sceneId,
//                        eventProvince,
//                        ownerProvince,
//                        messageBean.getTopic(),
//                        messageBean.getTopic().substring(0, 3),
//                        messageBean.getPartition(),
//                        messageBean.getOffset(),
//                        messageBean.getConsumerRecordTimestamp(),
//                        messageBean.getProcessTimestamp(),
//                        System.currentTimeMillis(),
//                        messageBean.getMessage()
//                );
            }
        })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "table_name",
                                "operation",
                                "user_id",
                                "SP_PRODUCT_ID",
                                "START_DATE",
                                "END_DATE",
                                "event_time",
                                "kafka_time",
                                "paimon_time"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.LOCAL_DATE_TIME,
                        Types.LOCAL_DATE_TIME
                ))
//                .returns(
//                Types.ROW_NAMED(
//                        new String[] {
//                                "conn_id",
//                                "date_id",
//                                "event_time",
//                                "receive_time",
//                                "integration_time",
//                                "root_time",
//                                "process_time",
//                                "release_time",
//                                "scene_id",
//                                "event_province",
//                                "owner_province",
//                                "topic",
//                                "topic_head3",
//                                "partition",
//                                "offset",
//                                "consumer_record_timestamp",
//                                "process_timestamp",
//                                "write_timestamp",
//                                "message"
//                        },
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.INT,
//                        Types.LONG,
//                        Types.LONG,
//                        Types.LONG,
//                        Types.LONG,
//                        Types.STRING
//                )
//        )
                .uid("toRow_TF_F_USER_SP").name("toRow_TF_F_USER_SP");

        Schema schema = Schema.newBuilder()
                .column("system", DataTypes.STRING())
                .column("table_name", DataTypes.STRING())
                .column("operation", DataTypes.STRING())
                .column("user_id", DataTypes.STRING())
                .column("SP_PRODUCT_ID", DataTypes.STRING())
                .column("START_DATE", DataTypes.STRING())
                .column("END_DATE", DataTypes.STRING())
                .column("event_time", DataTypes.TIMESTAMP(3))
                .column("kafka_time", DataTypes.TIMESTAMP(3))
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .build();

//        Table table = tableEnv.fromDataStream(mainWriteStream, schema);
        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("InputTable_TF_F_USER_SP", table);

/*        if (MyUtil.isLocal()) {
            tableEnv.executeSql("CREATE TABLE IF NOT EXISTS topic_save_data (\n" +
                    "conn_id STRING,\n" +
                    "date_id STRING,\n" +
                    "event_time STRING,\n" +
                    "receive_time STRING,\n" +
                    "integration_time STRING,\n" +
                    "root_time STRING,\n" +
                    "process_time STRING,\n" +
                    "release_time STRING,\n" +
                    "scene_id STRING,\n" +
                    "event_province STRING,\n" +
                    "owner_province STRING,\n" +
                    "topic STRING,\n" +
                    "topic_head3 STRING,\n" +
                    "`partition` int,\n" +
                    "`offset` bigint,\n" +
                    "consumer_record_timestamp bigint,\n" +
                    "process_timestamp bigint,\n" +
                    "write_timestamp bigint,\n" +
                    "message STRING\n" +
                    ") PARTITIONED BY (conn_id, date_id, topic_head3)");
        }*/

        String[] tableNameSplit = TableName_TF_F_USER_SP.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM InputTable_TF_F_USER_SP");
        }

        return returnlist;
    }

    public static List<String> getInsertSql_TF_F_USER(StreamTableEnvironment tableEnv, DataStream<MajorBean> stream, ParameterTool parameters) {
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_gray_flink");
        String tableName = parameters.get("table.name.TF_F_USER", "ods_r_paimon_tf_f_user");
        DataStream<Row> mainWriteStream = stream.flatMap(new FlatMapFunction<MajorBean, Row>() {
            @Override
            public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                String opt = valueIn.getOperation();
                Long timeStamp = valueIn.getTimeStamp();
                long second = timeStamp / 1000;
                int millisecond = (int) (timeStamp % 1000);

                Row row = Row.withPositions(16);
                row.setField(0, valueIn.getSystem());
                row.setField(1, valueIn.getTable_name());
                row.setField(2, valueIn.getOperation());
                row.setField(3, valueIn.getSERIAL_NUMBER());
                row.setField(4, valueIn.getUSER_ID());
                row.setField(5, valueIn.getPROVINCE_CODE());
                row.setField(6, valueIn.getEPARCHY_CODE());
                row.setField(7, valueIn.getREMOVE_TAG());
                row.setField(8, valueIn.getOPEN_DATE());
                row.setField(9, valueIn.getCREDIT_VALUE());
                row.setField(10, valueIn.getUSER_STATE_CODESET());
                row.setField(11, valueIn.getPRODUCT_ID());
                row.setField(12, valueIn.getNET_TYPE_CODE());
                row.setField(13, java.time.LocalDateTime.ofEpochSecond(second, millisecond * 1000000, ZoneOffset.ofHours(8)));
                row.setField(14, java.time.LocalDateTime.ofEpochSecond(second, millisecond * 1000000, ZoneOffset.ofHours(8)));
                row.setField(15, java.time.LocalDateTime.now());
                switch (opt) {
                    case "I":
                    case "U":
//                        row.setKind(RowKind.UPDATE_AFTER);
                        row.setKind(RowKind.INSERT);
                        out.collect(row);
                        break;
                    case "D":
                        row.setKind(RowKind.DELETE);
                        out.collect(row);
                        break;
                    default:
                }
            }
        })
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "table_name",
                                "operation",
                                "serial_number",
                                "user_id",
                                "province_code",
                                "EPARCHY_CODE",
                                "REMOVE_TAG",
                                "OPEN_DATE",
                                "CREDIT_VALUE",
                                "USER_STATE_CODESET",
                                "PRODUCT_ID",
                                "NET_TYPE_CODE",
                                "event_time",
                                "kafka_time",
                                "paimon_time"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.LOCAL_DATE_TIME,
                        Types.LOCAL_DATE_TIME,
                        Types.LOCAL_DATE_TIME
                ))
                .uid("toRow_TF_F_USER").name("toRow_TF_F_USER");

        Schema schema = Schema.newBuilder()
                .column("system", "STRING")
                .column("table_name", "STRING")
                .column("operation", "STRING")
                .column("serial_number", "STRING")
                .column("user_id", "STRING")
                .column("province_code", "STRING")
                .column("EPARCHY_CODE", "STRING")
                .column("REMOVE_TAG", "STRING")
                .column("OPEN_DATE", "STRING")
                .column("CREDIT_VALUE", "STRING")
                .column("USER_STATE_CODESET", "STRING")
                .column("PRODUCT_ID", "STRING")
                .column("NET_TYPE_CODE", "STRING")
                .column("event_time", DataTypes.TIMESTAMP(3))
                .column("kafka_time", DataTypes.TIMESTAMP(3))
                .column("paimon_time", DataTypes.TIMESTAMP(3))
                .build();

        Table table = tableEnv.fromChangelogStream(mainWriteStream, schema);

        tableEnv.createTemporaryView("InputTable_TF_F_USER", table);

        /*        tableEnv.executeSql("CREATE TABLE IF NOT EXISTS `paimon_catalog_0_4`.`ubd_sscj_gray_flink`.`ods_r_paimon_cb_all_20230705_pufc`(\n" +
//                " id STRING PRIMARY KEY,\n" +
                " `system` STRING,\n" +
                " table_name STRING,\n" +
                " operation STRING,\n" +
                " SERIAL_NUMBER STRING,\n" +
                " USER_ID STRING,\n" +
                " PROVINCE_CODE STRING,\n" +
                " EPARCHY_CODE STRING,\n" +
                " REMOVE_TAG STRING,\n" +
                " OPEN_DATE STRING,\n" +
                " CREDIT_VALUE STRING,\n" +
                " USER_STATE_CODESET STRING,\n" +
                " PRODUCT_ID STRING,\n" +
                " NET_TYPE_CODE STRING,\n" +
//                " dt BIGINT\n" +
                " event_time TIMESTAMP(3),\n" +
                " kafka_time TIMESTAMP(3),\n" +
                " paimon_time TIMESTAMP(3),\n" +
//                " pt STRING,\n" +
                " PRIMARY KEY (USER_ID,`system`) NOT ENFORCED \n" +
                ") partitioned by (`system`) WITH (\n" +
                " 'bucket' = '8',\n" +
                " 'merge-engine' = 'partial-update',\n" +
                " 'sequence.field' = 'paimon_time',\n" +
                " 'changelog-producer' = 'full-compaction'\n" +
//                " 'bucket' = '2',\n" +
//                " 'bucket-key' = 'USER_ID',\n" +
//                " 'write-mode' = 'append-only' \n" +
//                " 'snapshot.time-retained' = '1h',\n " +
//                " 'write-only' = 'true'\n" +
                ");");*/

        String[] tableNameSplit = tableName.split(",", -1);
        List<String> returnlist = new ArrayList<>();
        for (String s : tableNameSplit) {
            returnlist.add("INSERT INTO `paimon_catalog`.`" + defaultDatabase + "`.`" + s + "` SELECT * FROM InputTable_TF_F_USER");
        }

        return returnlist;
    }




/*    public static String getMetricInsertSql(StreamTableEnvironment tableEnv, DataStream<MetricStatBean> stream, String connId) {
        DataStream<Row> metricWriteStream = stream.map(new MapFunction<MetricStatBean, Row>() {
            @Override
            public Row map(MetricStatBean metricStatBean) throws Exception {
                String dateId = sd.format(System.currentTimeMillis());

                return Row.ofKind(RowKind.INSERT,
                        connId,
                        dateId,
                        metricStatBean.getTopic(),
                        metricStatBean.getTotalRecordNum(),
                        metricStatBean.getInternalTotalProcessTimeMs(),
                        metricStatBean.getInternalMinProcessTimeMs(),
                        metricStatBean.getInternalMaxProcessTimeMs(),
                        metricStatBean.getInternalAvgProcessTimeMs(),
                        System.currentTimeMillis()
                );
            }
        }).returns(
                Types.ROW_NAMED(
                        new String[] {
                                "conn_id",
                                "date_id",
                                "topic",
                                "total_record_num",
                                "internal_total_process_time",
                                "internal_min_process_time",
                                "internal_max_process_time",
                                "internal_avg_process_time",
                                "create_time"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.LONG,
                        Types.LONG,
                        Types.LONG,
                        Types.LONG,
                        Types.LONG,
                        Types.LONG
                )
        ).uid("MetricDataToRow").name("MetricDataToRow");

        Schema schema = Schema.newBuilder()
                .column("conn_id", DataTypes.STRING())
                .column("date_id", DataTypes.STRING())
                .column("topic", DataTypes.STRING())
                .column("total_record_num", DataTypes.BIGINT())
                .column("internal_total_process_time", DataTypes.BIGINT())
                .column("internal_min_process_time", DataTypes.BIGINT())
                .column("internal_max_process_time", DataTypes.BIGINT())
                .column("internal_avg_process_time", DataTypes.BIGINT())
                .column("create_time", DataTypes.BIGINT())
                .build();

        Table table = tableEnv.fromDataStream(metricWriteStream, schema);

        tableEnv.createTemporaryView("MetricInputTable", table);

        if (MyUtil.isLocal()) {
            tableEnv.executeSql("CREATE TABLE IF NOT EXISTS metric_stat (\n" +
                    "conn_id STRING,\n" +
                    "date_id STRING,\n" +
                    "topic STRING,\n" +
                    "total_record_num bigint,\n" +
                    "internal_total_process_time bigint,\n" +
                    "internal_min_process_time bigint,\n" +
                    "internal_max_process_time bigint,\n" +
                    "internal_avg_process_time bigint,\n" +
                    "create_time bigint\n" +
                    ") PARTITIONED BY (conn_id, date_id)");
        }

        //tableEnv.executeSql("INSERT INTO metric_stat SELECT * FROM MetricInputTable");
        return "INSERT INTO metric_stat SELECT * FROM MetricInputTable";
    }*/


}
