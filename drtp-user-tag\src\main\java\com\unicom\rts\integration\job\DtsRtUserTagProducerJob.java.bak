package com.unicom.rts.integration.job;

import cn.cu.flink.filter.DtsMsgRichFilterFunction;
import cn.cu.flink.process.DtsMessage2UtProcessFunction;
import cn.cu.flink.process.DtsMsgProcessFunction;
import cn.cu.flink.schema.ByteArraySchema;
import cn.cu.flink.sink.bucketassigners.MyDateTimeBucketAssigner;
import cn.cu.flink.sink.writerFactory.GzipBulkStringWriterFactory;
import cn.cu.message.dts.bean.DtsMessageRecordBean;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.fs.Path;
import org.apache.flink.runtime.state.filesystem.FsStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig.ExternalizedCheckpointCleanup;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.filesystem.OutputFileConfig;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.OnCheckpointRollingPolicy;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Properties;

public class DtsRtUserTagProducerJob {

    private final static Logger log = LoggerFactory.getLogger(DtsRtUserTagProducerJob.class);

    private int dataSourceParallelism;
    private String jobName;
    private long checkPointTime;
    private Path homePath;
    private ParameterTool parameters;
    private String tableName;
    private long initDate;
    private String msgType;

    public DtsRtUserTagProducerJob(ParameterTool parameters) {

        this.parameters = parameters;
        this.tableName = parameters.get("tableName").trim();
        this.msgType = parameters.get("msgType");
        this.initDate = Long.parseLong(parameters.get("initDate"));
        this.dataSourceParallelism = Integer.parseInt(parameters.get("dataSourceParallelism"));
        this.checkPointTime = Long.parseLong(parameters.get("checkPointTime"));
        homePath = new Path(parameters.get("pathStr"));
        this.jobName = parameters.get("jobName") +"_"+ msgType + "_" + tableName;
    }

    public void runJob() throws Exception {

        log.info("===============开始启动程序=============");

        // String configPath = parameters.get("config_path");
        // ParameterTool conf = ParameterTool.fromPropertiesFile(configPath);
        String checkPointPath = parameters.get("checkPointPath")+"_"+msgType+"_"+tableName;

        Configuration configuration = new Configuration();
        configuration.setBoolean("ipc.client.fallback-to-simple-auth-allowed", true);
        configuration.setString("yarn.application.name", jobName);

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(configuration);

        env.enableCheckpointing(checkPointTime);
        // 后端写hdfs文件
        env.setStateBackend(new FsStateBackend(checkPointPath));
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(checkPointTime / 5);
        env.getCheckpointConfig().setCheckpointTimeout(checkPointTime);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().enableExternalizedCheckpoints(ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // kafka配置文件
        Properties srcKafkaProp = new Properties();
        srcKafkaProp = ParameterTool.fromMap(parameters.toMap()).getProperties();
        srcKafkaProp.setProperty("group.id", srcKafkaProp.getProperty("group.id") + jobName);

        // flink连接kafka：拆成3个flink任务
        FlinkKafkaConsumer<byte[]> consumer = new FlinkKafkaConsumer<byte[]>(
                Arrays.asList(srcKafkaProp.getProperty("dtsTopicInfo").split(",")), new ByteArraySchema(),
                srcKafkaProp);
        if (Boolean.parseBoolean(parameters.get("isStartFromTimestamp"))) {
            consumer.setStartFromTimestamp(Long.parseLong(parameters.get("startFromTimestamp")));
        }

        // 消费kafka数据
        DataStreamSource<byte[]> srcData = env.addSource(consumer, "kafkaSrc")
                .setParallelism(dataSourceParallelism);

        // 解析kafka数据
        DataStream<DtsMessageRecordBean> msgStrean = srcData
                .map(new DtsMsgProcessFunction()).uid("DtsMsgProcessFunction").name("parseMsg")
                //TODO：改造此处，适配所有过滤逻辑
                .filter(new DtsMsgRichFilterFunction(tableName, initDate)).uid("DtsMsgRichFilterFunction").name("filterMsg");

        // 处理标签
        DataStream<String> msg2StringStream = msgStrean
                // 根据数据生成标签插入hbase
                .map(new DtsMessage2UtProcessFunction(tableName,parameters)).uid("DtsMessage2UtProcessFunction").name(msgType+"MsgGenDt");
                //生成json字符串，后续写入hdfs，排查问题用

        OutputFileConfig outputFileConfig = new OutputFileConfig("part", ".gz");

        // 准备数据写入hdfs
        StreamingFileSink<String> oggMsgStreamingFileSinkForBulkFormat = StreamingFileSink
                .forBulkFormat(homePath, new GzipBulkStringWriterFactory())
                .withBucketAssigner(new MyDateTimeBucketAssigner<String>("yyyyMMdd", msgType+"_"+tableName))
                .withRollingPolicy(OnCheckpointRollingPolicy.build())
                .withBucketCheckInterval(10)
                .withOutputFileConfig(outputFileConfig)
                .build();

        msg2StringStream.addSink(oggMsgStreamingFileSinkForBulkFormat).uid("writeToHdfs").name("writeToHdfs");

        env.execute(jobName);

        log.info("==============程序提交完毕=============");

    }

}
