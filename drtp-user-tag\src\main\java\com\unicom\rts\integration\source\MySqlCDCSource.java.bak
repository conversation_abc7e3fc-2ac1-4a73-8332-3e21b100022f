package com.unicom.rts.integration.source;

import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/19 10:03
 */
public class MySqlCDCSource {
    private final static Logger log = LoggerFactory.getLogger(MySqlCDCSource.class);

    public static MySqlSource<String> create(ParameterTool conf, String serverIdRange) throws Exception {
        log.info("tableList:{},{}", conf.get("mysql.cdc.table.rule"), conf.get("mysql.cdc.table.trade"));
        log.info("set serverIdRange:" + serverIdRange);
        Properties debeziumProperties = new Properties();
        // 配置CDC扫描表时不加锁，加锁需要reload权限
        debeziumProperties.put("snapshot.locking.mode", "none");
        MySqlSource<String> mySqlSource = MySqlSource.<String>builder()
                .hostname(conf.get("mysql.ip","***********"))
                .port(conf.getInt("mysql.port", 18081))
                .databaseList(conf.get("mysql.cdc.database", "rts_db_gray")) // set captured database
//                .tableList(conf.get("mysql.cdc.table.rule", "rts_db_gray.rule_submit_info"), conf.get("mysql.cdc.table.trade", "rts_db_gray.trade_area_lacci_mapping")) // set captured table
                .tableList(conf.get("mysql.cdc.table.trade", "rts_db_gray.trade_area_lacci_mapping")) // set captured table
                .username(conf.get("mysql.user", "gray_rts_db"))
                .password(conf.get("mysql.password", "GrayRTS@2022"))
                .serverId(serverIdRange)
                .deserializer(new CDCJsonDSchema()) // converts SourceRecord to JSON String
                .startupOptions(StartupOptions.initial())
                .splitSize(conf.getInt("scan.incremental.snapshot.chunk.size", 8096))
                .distributionFactorUpper(conf.getDouble("split-key.even-distribution.factor.upper-bound", 1.0d))
                .debeziumProperties(debeziumProperties)
                .build();
        return mySqlSource;
    }
}