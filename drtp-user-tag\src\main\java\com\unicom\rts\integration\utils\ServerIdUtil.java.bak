package com.unicom.rts.integration.utils;

import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/6/6 14:31
 */
public class ServerIdUtil {
    private final static Logger log = LoggerFactory.getLogger(ServerIdUtil.class);

    public static String serverIdGenerator(ParameterTool conf) throws Exception {
        String jdbcUrl = conf.get("jdbcUrl","**********************************************************************************");
        String user = conf.get("mysql.user","gray_rts_db");
        String password = conf.get("mysql.password","GrayRTS@2022");
        return serverIdGenerator(conf.getInt("mysql.cdc.parallelism", 1), jdbcUrl, user, password);
    }

    public static String serverIdGenerator(int p, String jdbcUrl, String user, String password) throws Exception {
        int i = 0;
        String serverIdRange = "";
        while (true) {
            Connection connection = getConnection(jdbcUrl, user, password);
            ResultSet resultSet = null;
            PreparedStatement maxIdPS = null;
            PreparedStatement updatePS = null;
            ResultSet resultSetAfter = null;
            try {
                i++;
                if (i == 6) {
                    break;
                }
                String maxIdSQL = "select max_id from cdc_server_id_generator where id = 1 ";
//                String updateSQL = "UPDATE `cdc_server_id_generator` SET `max_id` = max_id + " + (p + 1) + ", `version` = version + 1 where id =1";
                String updateSQL = String.join("","UPDATE `cdc_server_id_generator` SET `max_id` = max_id + ",(p + 1)+"",", `version` = version + 1 where id =1");
                if (connection != null) {
                    maxIdPS = connection.prepareStatement(maxIdSQL);
                    resultSet = maxIdPS.executeQuery();
                    int maxIdBefore = 0;
                    while (resultSet.next()) {
                        maxIdBefore = resultSet.getInt("max_id");
                    }
                    int end = maxIdBefore + p + 1;
                    updatePS = connection.prepareStatement(updateSQL);
                    updatePS.executeUpdate();
                    resultSetAfter = maxIdPS.executeQuery();
                    int maxIdAfter = 0;
                    while (resultSetAfter.next()) {
                        maxIdAfter = resultSetAfter.getInt("max_id");
                    }
                    log.warn("generate server_id maxIdBefore:" + maxIdBefore + " maxIdAfter:" + maxIdAfter + " calculate:" + end);
                    if (maxIdAfter == end) {
                        serverIdRange = (maxIdBefore + 1) + "-" + maxIdAfter;
                        break;
                    } else {
                        if (connection != null) {
                            connection.close();
                        }
                        Thread.sleep(500);
                    }
                }
            } catch (Exception e) {
                log.error("Exception {}", e);
            } finally {
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException throwables) {
                        log.error("SQLException {}", throwables);
                    }
                }
                assert resultSet != null;
                resultSet.close();

                assert maxIdPS != null;
                maxIdPS.close();

                assert updatePS != null;
                updatePS.close();

                assert resultSetAfter != null;
                resultSetAfter.close();
            }
        }

        return serverIdRange;
    }

    private static Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            Class.forName("com.mysql.jdbc.Driver");
            //注意，改成配置参数 数据库地址和用户名、密码
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            log.error("-----------mysql get connection has exception , msg = {}", e.toString());
        }
        return con;
    }
}
