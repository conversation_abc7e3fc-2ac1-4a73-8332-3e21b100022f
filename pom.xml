<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.unicom.rts.integration</groupId>
    <artifactId>rts-data-realtime-tag-process</artifactId>
    <version>1.0</version>
    <modules>
        <module>drtp-user-tag</module>
        <module>drtp-common</module>
    </modules>
    <packaging>pom</packaging>

    <name>parent</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <flink.version>1.17.0</flink.version>
        <target.java.version>1.8</target.java.version>
        <scala.binary.version>2.11</scala.binary.version>
        <hbase.version>2.0.2</hbase.version>
        <maven.compiler.source>${target.java.version}</maven.compiler.source>
        <maven.compiler.target>${target.java.version}</maven.compiler.target>
        <log4j.version>2.17.1</log4j.version>
    </properties>
<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-shade-plugin</artifactId>-->
<!--                <version>3.1.1</version>-->
<!--                <configuration>-->
<!--                    <createDependencyReducedPom>false</createDependencyReducedPom>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-shade-plugin</artifactId>-->
<!--                <version>3.1.1</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>shade</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <artifactSet>-->
<!--                                <excludes>-->
<!--                                    <exclude>com.google.code.findbugs:jsr305</exclude>-->
<!--                                </excludes>-->
<!--                            </artifactSet>-->
<!--                            <filters>-->
<!--                                <filter>-->
<!--                                    &lt;!&ndash; Do not copy the signatures in the META-INF folder.-->
<!--                                    Otherwise, this might cause SecurityExceptions when using the JAR. &ndash;&gt;-->
<!--                                    <artifact>*:*</artifact>-->
<!--                                    <excludes>-->
<!--                                        <exclude>META-INF/*.SF</exclude>-->
<!--                                        <exclude>META-INF/*.DSA</exclude>-->
<!--                                        <exclude>META-INF/*.RSA</exclude>-->
<!--                                    </excludes>-->
<!--                                </filter>-->
<!--                            </filters>-->
<!--                            <transformers>-->
<!--                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">-->
<!--                                    &lt;!&ndash; Replace this with the main class of your job &ndash;&gt;-->
<!--                                    <mainClass>my.programs.main.clazz</mainClass>-->
<!--                                </transformer>-->
<!--                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>-->
<!--                            </transformers>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
</project>
