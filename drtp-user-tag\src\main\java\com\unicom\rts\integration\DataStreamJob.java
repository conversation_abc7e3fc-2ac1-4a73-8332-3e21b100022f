package com.unicom.rts.integration;

//import com.sun.org.slf4j.internal.Logger;
//import com.sun.org.slf4j.internal.LoggerFactory;
import com.unicom.rts.integration.job.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

/**
 * <AUTHOR>
 * @Date ${DATE} ${TIME}
 * @Description
 */
@Slf4j
public class DataStreamJob {
//    private final static Logger log = LoggerFactory.getLogger(DataStreamJob.class);
    public static void main(String[] args) throws Exception {
        System.out.println("Hello world!");
        ParameterTool parameters = ParameterTool.fromArgs(args);
        String msgType = parameters.get("msgType","unified");

//        try {
            if("ogg".equals(msgType)){
//                new OggRtUserTagProducerJob(parameters).runJob();
            }else if("unified".equals(msgType)){
//                new WriteToUnifiedKafkaJob(parameters).runJob();
                new RealtimeTagJob(parameters).runJob();
//            }else if("dts".equals(msgType)){
//                new DtsRtUserTagProducerJob(parameters).runJob();
            }else{
                throw new Exception("消息类型参数错误！");
            }
//        } catch (Exception e) {
            // e.printStackTrace();
//            log.error(String.valueOf(e));
//        }

    }
}