package com.unicom.rts.integration.function;


import com.alibaba.fastjson.JSONObject;
import com.unicom.realtime.bean.LacCiInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class TradeAreaFlatMap extends RichFlatMapFunction<String, LacCiInfo> {
    private final static Logger logger = LoggerFactory.getLogger(TradeAreaFlatMap.class);
    private ParameterTool conf;

    public TradeAreaFlatMap(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void flatMap(String value, Collector collector) {
        try {
            //获取省分ID，只加载本省围栏变化数据
//            String provId = tradeAreaJson.getJSONObject("value").getString("prov_id");
//            if (this.provId.equals(provId)) {
            JSONObject tradeAreaJson = JSONObject.parseObject(value);
            Integer tradeId = tradeAreaJson.getJSONObject("value").getInteger("trade_area_id");
            String operation = tradeAreaJson.getString("op");
            String lacCiListStr = tradeAreaJson.getJSONObject("value").getString("lacci_list");
            String[] lacCiListArr = StringUtils.splitPreserveAllTokens(lacCiListStr, ",");

            if ("u".equals(operation)) {
                //针对lacci_list字段更新的情况，特殊处理
                String beforeLacCiListStr = tradeAreaJson.getJSONObject("before").getString("lacci_list");
                getUpdateLacCiInfo(beforeLacCiListStr, lacCiListStr, tradeId, collector);
                return;
            } else {
                //切分基站信息和围栏ID，以及增删改查操作，发送到下游
                parseLacCiInfo(Arrays.asList(lacCiListArr), tradeId, operation, collector);
            }

//            }
        } catch (Exception e) {
            logger.error("Exception:{} ;tradeAreaJson {}", e, value);
        }
    }

    public static void parseLacCiInfo(Collection<String> lacCiInfos, Integer tradeId, String operation, Collector collector) {
        lacCiInfos.stream().forEach(lacCiInfoStr -> {
            String[] lacCiInfoArr = StringUtils.splitPreserveAllTokens(lacCiInfoStr, "|");
            String lacCi = lacCiInfoArr[0];
            String lon = lacCiInfoArr[1];
            String lat = lacCiInfoArr[2];
            String area = lacCiInfoArr[3];
            String city = lacCiInfoArr[4];

            LacCiInfo lacCiInfo = new LacCiInfo(lacCi, lon, lat, area, city, tradeId, operation);
            collector.collect(lacCiInfo);
        });
    }

    public static void getUpdateLacCiInfo(String beforeStr, String afterStr, Integer tradeId, Collector collector) {
        List beforeArr = Arrays.asList(StringUtils.splitPreserveAllTokens(beforeStr, ","));
        List afterArr = Arrays.asList(StringUtils.splitPreserveAllTokens(afterStr, ","));

        //删除的基站
        Collection<String> delete = CollectionUtils.subtract(beforeArr, afterArr);
        parseLacCiInfo(delete, tradeId, "d", collector);

        //新增的基站
        Collection insert = CollectionUtils.subtract(afterArr, beforeArr);
        parseLacCiInfo(insert, tradeId, "c", collector);
    }

}
