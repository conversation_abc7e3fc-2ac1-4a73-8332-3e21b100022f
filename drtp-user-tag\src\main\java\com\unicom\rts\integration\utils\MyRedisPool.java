package com.unicom.rts.integration.utils;

import org.apache.flink.api.java.utils.ParameterTool;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023/2/13 22:59
 * @Description
 */
public class MyRedisPool {

    private static JedisCluster jedisCluster = null;

    public static synchronized JedisCluster getJedisPool(ParameterTool conf) {
        // 只有当jedisCluster为空时才实例化
        if (jedisCluster == null) {
            JedisPoolConfig config = new JedisPoolConfig();
            Integer timeout = 5000;
            config.setMaxTotal(10000);//最大空闲连接数
            config.setMaxIdle(5);
            config.setMinIdle(1);
            config.setMaxWaitMillis(10000L);//获取连接时的最大等待毫秒数,小于零:阻塞不确定的时间,默认-1
            config.setTestOnBorrow(false);//在获取连接的时候检查有效性, 默认false
            config.setTestWhileIdle(true);//在空闲时检查有效性, 默认false
            config.setTestOnReturn(false);

            //redis集群，多个节点
            String serverip = conf.get("redis.hosts","************:32005,************:32006,************:32007,************:32008,************:32009,************:32010,************:32011,************:32012,************:32013,************:32014,************:32015,************:32016,************:32017,************:32018,************:32019,*************:32006,*************:32007,*************:32008,*************:32009,*************:32010,*************:32011,*************:32012,*************:32013,*************:32014,*************:32015,*************:32016,*************:32017,*************:32018,*************:32019,*************:32020,*************:32006,*************:32007,*************:32008,*************:32009,*************:32010,*************:32011,*************:32012,*************:32013,*************:32014,*************:32015,*************:32016,*************:32017,*************:32018,*************:32019,*************:32020,*************:32006,*************:32007,*************:32008,*************:32009,*************:32010,*************:32011,*************:32012,*************:32013,*************:32014,*************:32015,*************:32016,*************:32017,*************:32018,*************:32019,*************:32020,*************:32006,*************:32007,*************:32008,*************:32009,*************:32010,*************:32011,*************:32012,*************:32013,*************:32014,*************:32015,*************:32006,*************:32007,*************:32008,*************:32009,*************:32010,*************:32011,*************:32012,*************:32013,*************:32015,*************:32016").trim();
            String password = conf.get("redis.auth.password", "Sscj#process$202304axef").trim();
            String[] serveripArray = serverip.split(",");

            Set<HostAndPort> nodes = new LinkedHashSet<HostAndPort>();
            for (String str : serveripArray) {
                String ip = str.split(":")[0];
                int port = Integer.parseInt(str.split(":")[1]);
                nodes.add(new HostAndPort(ip, port));
            }
            jedisCluster = new JedisCluster(nodes, timeout, timeout, 5, password,config);

        }
        return jedisCluster;
    }
}
