package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
public class TF_F_USER_Mapper extends RichFlatMapFunction<Row, MajorBean> {
    ParameterTool conf;
    String tableName;
    boolean isTF_F_USER;
    public TF_F_USER_Mapper(ParameterTool conf) {
        this.conf = conf;
        tableName = conf.get("tableName", "tf_f_user");
        isTF_F_USER = "tf_f_user".equalsIgnoreCase(tableName);
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        long currentTimeMillis = System.currentTimeMillis();
        // PARTITION_ID,USER_ID,CUST_ID,USECUST_ID,BRAND_CODE,PRODUCT_ID,EPARCHY_CODE,CITY_CODE,USER_PASSWD,USER_DIFF_CODE,USER_TYPE_CODE,SERIAL_NUMBER,NET_TYPE_CODE,SCORE_VALUE,CREDIT_CLASS,BASIC_CREDIT_VALUE,CREDIT_VALUE,ACCT_TAG,PREPAY_TAG,IN_DATE,OPEN_DATE,OPEN_MODE,OPEN_DEPART_ID,OPEN_STAFF_ID,IN_DEPART_ID,IN_STAFF_ID,REMOVE_TAG,DESTROY_TIME,REMOVE_EPARCHY_CODE,REMOVE_CITY_CODE,REMOVE_DEPART_ID,REMOVE_REASON_CODE,PRE_DESTROY_TIME,FIRST_CALL_TIME,LAST_STOP_TIME,USER_STATE_CODESET,MPUTE_MONTH_FEE,MPUTE_DATE,UPDATE_TIME,ASSURE_CUST_ID,ASSURE_TYPE_CODE,ASSURE_DATE,DEVELOP_STAFF_ID,DEVELOP_DATE,DEVELOP_EPARCHY_CODE,DEVELOP_CITY_CODE,DEVELOP_DEPART_ID,DEVELOP_NO,REMARK,CREDIT_RULE_ID,CONTRACT_ID,CHANGEUSER_DATE,IN_NET_MODE,PRODUCT_TYPE_CODE,MAIN_DISCNT_CODE,PRODUCT_SPEC,PROVINCE_CODE,opt,optTime,cdhtime,dataSource,in_time,database_tag
        String user_id = String.valueOf(value.getField("USER_ID"));
        if (StringUtils.isBlank(user_id)) {
            return;
        }
        MajorBean majorBean = new MajorBean();
        majorBean.setUserId(user_id);
        String province_code = String.valueOf(value.getField("PROVINCE_CODE"));
        if (3==province_code.length()) {
            province_code = province_code.substring(1);
        }
        majorBean.setPROVINCE_CODE(province_code);
        majorBean.setSERIAL_NUMBER(String.valueOf(value.getField("SERIAL_NUMBER")));
        majorBean.setEPARCHY_CODE(String.valueOf(value.getField("EPARCHY_CODE")));
        majorBean.setNET_TYPE_CODE(String.valueOf(value.getField("NET_TYPE_CODE")));
        majorBean.setPRODUCT_ID(String.valueOf(value.getField("PRODUCT_ID")));
        majorBean.setBRAND_CODE(String.valueOf(value.getField("BRAND_CODE")));
        majorBean.setREMOVE_TAG(String.valueOf(value.getField("REMOVE_TAG")));
        majorBean.setMyDataSource("TF_F_USER");
        majorBean.setRowKind(value.getKind());
        majorBean.setOpt(String.valueOf(value.getField("OPT")));
        majorBean.setOptTime(String.valueOf(value.getField("OPTTIME")));
//                majorBean.setCdhtime(String.valueOf(value.getField("cdhtime")));
        majorBean.setKafka_in_time((LocalDateTime) value.getField("KAFKA_TIME"));
//                majorBean.setKafka_out_time(String.valueOf(value.getField("kafka_out_time")));
//                majorBean.setPaimon_time(String.valueOf(value.getField("paimon_time")));
        majorBean.setDataSource(String.valueOf(value.getField("DATASOURCE")));
        majorBean.setDatabase_tag(String.valueOf(value.getField("DATABASE_TAG")));
        try {
            majorBean.parseHeader2((Map<String, byte[]>) value.getField("HEADERS"));
        } catch (Exception e) {
            majorBean.parseHeader4();
        }
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));

        if (isTF_F_USER) {
            majorBean.setOPEN_DATE(String.valueOf(value.getField("OPEN_DATE")));
            majorBean.setUSER_STATE_CODESET(String.valueOf(value.getField("USER_STATE_CODESET")));
            majorBean.setCREDIT_VALUE(String.valueOf(value.getField("CREDIT_VALUE")));
        }

        out.collect(majorBean);
    }
}
