package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
@Slf4j
public class CMW_CUST_MANAGER_REF_MAPPER extends RichFlatMapFunction<Row, MajorBean> {
    ParameterTool conf;
    Long end_date_earliest;
    SimpleDateFormat simpleDateFormat = null;
    SimpleDateFormat simpleDateFormat2 = null;
    public CMW_CUST_MANAGER_REF_MAPPER(ParameterTool conf) {
        this.conf = conf;
        end_date_earliest = Long.parseLong(conf.get("table.end_date.ms", "1698854400000"));
        simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd:HH:mm:ss");
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        if(value.getField("IS_VIP".toLowerCase()) == null){
            return;
        }
        if(value.getField("user_id".toLowerCase()) == null){
            return;
        }
        if(value.getField("IS_MAIN".toLowerCase()) == null){
            return;
        }
        if(value.getField("IS_RECOVER".toLowerCase()) == null){
            return;
        }

        String user_id = String.valueOf(value.getField("USER_ID".toLowerCase()));
        if (StringUtils.isBlank(user_id)) {
            return;
        }
        if((StringUtils.isBlank((value.getField("IS_MAIN".toLowerCase()).toString())))||value.getField("IS_MAIN".toLowerCase()).toString().equals("\\N")) {
            return;
        }
        if((StringUtils.isBlank((value.getField("IS_RECOVER".toLowerCase()).toString())))||value.getField("IS_RECOVER".toLowerCase()).toString().equals("\\N")) {
            return;
        }

        if((StringUtils.isBlank((value.getField("IS_VIP".toLowerCase()).toString())))||value.getField("IS_VIP".toLowerCase()).toString().equals("\\N")) {
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        // PRIMARY_USER_ID,PRIMARY_SERIAL_NUMBER,MEM_USER_ID,MEM_SERIAL_NUMBER,RELATION_TYPE_CODE,PRIMARY_ROLE_CODE,MEM_ROLE_CODE,BILLING_MEM_ROLE_CODE,BILLING_ADD_TAG,DISCNT_PRIORITY,MEM_SHORT_NUM,START_DATE,END_DATE,REL_ITEM_ID,UPDATE_TIME,MEM_EPARCHY_CODE,MEM_PROVINCE_CODE,PRIMARY_EPARCHY_CODE,PRIMARY_PROVINCE_CODE,opt,optTime,cdhtime


        String end_date = String.valueOf(value.getField("UPDATE_TIME".toLowerCase()));
        String opt = String.valueOf(value.getField("opt"));
        long end_date_l = 0L;
        if(StringUtils.isNotBlank(end_date)) {
            try {
                // date format conversion
                if (end_date.contains(":")) {
                    if (end_date.contains(" ")) {
                        end_date_l = simpleDateFormat.parse(end_date).getTime();
                    } else {
                        end_date_l = simpleDateFormat2.parse(end_date).getTime();
                    }
                } else {
                    end_date_l = Long.parseLong(end_date);
                }
                if (end_date_l < 1000000000000L) {
                    throw new Exception("MY-ERROR-SubMapper: end_date < 1000000000000L Exception!");
                }
            } catch (Exception e) {
                log.error("MY-ERROR-SubMapper-parseError=" + e);
                return;
            }
        }
        if ("Init".equals(opt) && end_date_l < end_date_earliest) {
            return;
        }

        MajorBean majorBean = new MajorBean();
        majorBean.setUserId(user_id);
        majorBean.setEND_DATE(end_date);
        majorBean.setEnd_date_long(end_date_l);
        majorBean.setSERIAL_NUMBER(value.getField("CUSTOMER_MOBILE".toLowerCase()) == null? null: value.getField("CUSTOMER_MOBILE".toLowerCase()).toString());
        majorBean.setManagerId(value.getField("MANAGER_ID".toLowerCase()) == null? null: value.getField("MANAGER_ID".toLowerCase()).toString());
        majorBean.setMyDataSource("CMW_CUST_MANAGER_REF_MAPPER");
        majorBean.setIsMain(value.getField("IS_MAIN".toLowerCase()) == null? null: value.getField("IS_MAIN".toLowerCase()).toString());
        majorBean.setIsRecover(value.getField("IS_RECOVER".toLowerCase()) == null? null: value.getField("IS_RECOVER".toLowerCase()).toString());
        majorBean.setIsVip(value.getField("IS_VIP".toLowerCase()) == null? null: value.getField("IS_VIP".toLowerCase()).toString());
        majorBean.setRowKind(value.getKind());
        majorBean.setOpt(opt);
        majorBean.setOptTime(String.valueOf(value.getField("opttime")));
//                majorBean.setCdhtime(String.valueOf(value.getField("cdhtime")));
        majorBean.setKafka_in_time((LocalDateTime) value.getField("kafka_in_time"));
//                majorBean.setKafka_out_time(String.valueOf(value.getField("kafka_out_time")));
//                majorBean.setPaimon_time(String.valueOf(value.getField("paimon_time")));
        majorBean.parseHeader2((Map<String, byte[]>) value.getField("headers"));
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));
        out.collect(majorBean);
    }
}
