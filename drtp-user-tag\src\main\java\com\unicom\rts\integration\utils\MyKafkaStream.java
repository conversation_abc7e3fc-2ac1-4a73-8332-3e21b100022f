package com.unicom.rts.integration.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.TopicSelector;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class MyKafkaStream {
    private static final String SOURCE_SUFFIX = "_Source";
    private static final String SINK_SUFFIX = "_Sink";
    private static final String SOURCE_PREFIX = "source.kafka.properties.";
    private static final String SINK_PREFIX = "sink.kafka.properties.";

    public static <T> DataStream<T> addSource(StreamExecutionEnvironment env, ParameterTool conf, String topic, KafkaRecordDeserializationSchema<T> deserializationSchema) {
        int parallelism = conf.getInt("source.parallelism", 1);
        Properties properties = toProperties(conf, SOURCE_PREFIX);
        String startFrom = conf.get("kafkaConsumerStartFrom", "GroupOffsets");
        Long startFromTimeStamp = conf.getLong("kafkaConsumerStartFromTimeStamp", 0L);
        OffsetsInitializer initializer = OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST);
        if (startFromTimeStamp > 0L) {
            initializer = OffsetsInitializer.timestamp(startFromTimeStamp);
        } else if ("earliest".equals(startFrom)) {
            initializer = OffsetsInitializer.earliest();
        } else if ("latest".equals(startFrom)) {
            initializer = OffsetsInitializer.latest();
        }

        KafkaSource<T> source = KafkaSource.<T>builder()
                .setBootstrapServers(conf.get(SOURCE_PREFIX + "bootstrap","10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092"))
                .setGroupId(conf.get(SOURCE_PREFIX + "group","sscj-tag-test-20230530"))
                .setTopics(Arrays.asList(StringUtils.splitPreserveAllTokens(topic, ",")))
                .setProperties(properties)
                .setProperty("partition.discovery.interval.ms", "60000")
                .setStartingOffsets(initializer)
                .setDeserializer(deserializationSchema)
                .build();

        return env.fromSource(source, WatermarkStrategy.noWatermarks(), topic + SOURCE_SUFFIX).uid(topic + SOURCE_SUFFIX).setParallelism(parallelism);
    }

    public static DataStream<String> addSource(StreamExecutionEnvironment env, ParameterTool conf, String topic) {
        return addSource(env, conf, topic, KafkaRecordDeserializationSchema.valueOnly(StringDeserializer.class));
    }

    public static void addSink(DataStream<Tuple3<String, String, Iterable<Header>>> stream, ParameterTool conf) {
        Properties properties = toProperties2(conf, SINK_PREFIX);

        final TopicSelector<Tuple3<String, String, Iterable<Header>>> topicSelector =
                (tp) -> {
                    return tp.f0;
                };

//        final HeaderSelector<Tuple3<String, String, Iterable<Header>>> headerSelector =
//                (tp) -> {
//                    return tp.f2;
//                };

        KafkaSink<Tuple3<String, String, Iterable<Header>>> sink = KafkaSink.<Tuple3<String, String, Iterable<Header>>>builder()
                .setBootstrapServers(conf.get(SINK_PREFIX + "bootstrap","10.162.235.1:9092,10.162.235.3:9092,10.162.235.4:9092,10.162.235.5:9092,10.162.235.6:9092,10.162.235.7:9092,10.162.235.8:9092,10.162.235.9:9092,10.162.235.10:9092,10.162.235.11:9092,10.162.235.12:9092,10.162.235.13:9092,10.162.235.14:9092,10.162.235.15:9092,10.162.235.16:9092,10.162.235.17:9092,10.162.235.18:9092,10.162.235.19:9092,10.162.235.20:9092,10.162.235.21:9092,10.162.235.22:9092,10.162.235.23:9092,10.162.235.24:9092,10.162.235.25:9092,10.162.235.26:9092,10.162.235.27:9092,10.162.235.28:9092,10.162.235.29:9092"))
                .setKafkaProducerConfig(properties)
                .setRecordSerializer(new MultiTopicSerializationSchema()
                        /*KafkaRecordSerializationSchema.builder()
                        .setTopicSelector(topicSelector)
                        .setHeaderSelector(headerSelector)
                        .setValueSerializationSchema(new MultiTopicSchema())
                        .build()*/
                )
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();

        stream.sinkTo(sink).uid("KafkaMulti" + SINK_SUFFIX).setParallelism(conf.getInt("sink.parallelism",1));
    }

    public static Properties toProperties(ParameterTool conf, String prefix) {
        Properties properties = new Properties();
        // source Default Configuration
        properties.put("enable.auto.commit", conf.get("enable.auto.commit", "true"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset", "latest"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        // sink Default Configuration
        properties.put("acks", conf.get("acks", "0"));
        properties.put("retries", conf.get("retries", "3"));
        properties.put("batch.size", conf.get("batch.size", "16384"));
        properties.put("linger.ms", conf.get("linger.ms", "50"));
        properties.put("buffer.memory", conf.get("buffer.memory", "33554432"));
        properties.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));

        conf.toMap().keySet().stream().filter(key -> key.startsWith(prefix)).forEach(key -> properties.put(key.replaceFirst(prefix, ""), conf.get(key)));

        Boolean security = conf.getBoolean(prefix + "security", false);
        if (security) {
            //kafka安全设置
            String kafkaUser = conf.get(prefix + "user");
            String kafkaPassword = conf.get(prefix + "password","tianGong#123");
            properties.setProperty("sasl.jaas.config", conf.get(prefix + "sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule") + " required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", conf.get(prefix + "security.protocol", "SASL_PLAINTEXT"));
            properties.setProperty("sasl.mechanism", conf.get(prefix + "sasl.mechanism", "SCRAM-SHA-256"));
        }

        return properties;
    }
    public static Properties toProperties2(ParameterTool conf, String prefix) {
        Properties properties = new Properties();
        // source Default Configuration
        properties.put("enable.auto.commit", conf.get("enable.auto.commit", "true"));
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("auto.offset.reset", conf.get("auto.offset.reset", "latest"));
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        // sink Default Configuration
        properties.put("acks", conf.get("acks", "0"));
        properties.put("retries", conf.get("retries", "3"));
        properties.put("batch.size", conf.get("batch.size", "16384"));
        properties.put("linger.ms", conf.get("linger.ms", "50"));
        properties.put("buffer.memory", conf.get("buffer.memory", "33554432"));
        properties.put("request.timeout.ms", conf.get("request.timeout.ms", "120000"));

        conf.toMap().keySet().stream().filter(key -> key.startsWith(prefix)).forEach(key -> properties.put(key.replaceFirst(prefix, ""), conf.get(key)));

        Boolean security = conf.getBoolean(prefix + "security", false);
        if (security) {
            //kafka安全设置
            String kafkaUser = conf.get(prefix + "user");
            String kafkaPassword = conf.get(prefix + "password","tianGong#123");
            properties.setProperty("sasl.jaas.config", conf.get(prefix + "sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule") + " required username=\"" + kafkaUser + "\" password=\"" + kafkaPassword + "\";");
            properties.setProperty("security.protocol", conf.get(prefix + "security.protocol", "SASL_PLAINTEXT"));
            properties.setProperty("sasl.mechanism", conf.get(prefix + "sasl.mechanism", "PLAIN"));
        }

        return properties;
    }

    /**
     * 解析配置
     *
     * @param conf
     * @param prefix
     * @return
     */
    public static ParameterTool mergeConf(ParameterTool conf, String prefix) {
        Map<String, String> tmpMap = new HashMap<>();
        conf.toMap().keySet().stream().filter(key -> key.startsWith(prefix)).forEach(key -> tmpMap.put(key.replaceFirst(prefix, ""), conf.get(key)));
        ParameterTool tmpConf = ParameterTool.fromMap(tmpMap);
        return conf.mergeWith(tmpConf);
    }
}
