package com.unicom.rts.integration.job;


import com.unicom.rts.integration.bean.MajorBean;
//import com.unicom.rts.integration.process.OggMessage2UtProcessFunction;
import com.unicom.rts.integration.source.MyDeserializationSchema;
import com.unicom.rts.integration.utils.MyKafkaStream;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SideOutputDataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

public class OggRtUserTagProducerJob {

    private final static Logger log = LoggerFactory.getLogger(OggRtUserTagProducerJob.class);
    private final int dataSourceParallelism;
    private final String jobName;
    private final long checkPointTime;
    private final Path homePath;
    private final ParameterTool parameters;
    private final String tableName;
    private final long initDate;
    private final String msgType;

    public OggRtUserTagProducerJob(ParameterTool parameters) {

        this.parameters = parameters;
        this.tableName = parameters.get("tableName","tf_f_user").trim();
        this.msgType = parameters.get("msgType","1");
        this.initDate = parameters.getLong("initDate",0L);
        this.dataSourceParallelism = parameters.getInt("dataSourceParallelism",1);
        this.checkPointTime = parameters.getLong("checkPointTime", 600000L);
        homePath = new Path(parameters.get("pathStr","pathStr"));
        this.jobName = parameters.get("jobName","user-tag") +"_"+ msgType + "_" + tableName;
    }

    public void runJob() throws Exception {

        log.info("===============开始启动程序=============");
//        String checkPointPath = parameters.get("checkPointPath")+"_"+msgType+"_"+tableName;
//
//        Configuration configuration = new Configuration();
//        configuration.setBoolean("ipc.client.fallback-to-simple-auth-allowed", true);
//        configuration.setString("yarn.application.name", jobName);
//
//        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(configuration);
//        env.disableOperatorChaining();
//
//        env.enableCheckpointing(checkPointTime);
//        // 后端写hdfs文件
//        env.setStateBackend(new FsStateBackend(checkPointPath));
//        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
//        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(checkPointTime / 5);
//        env.getCheckpointConfig().setCheckpointTimeout(checkPointTime);
//        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
//        env.getCheckpointConfig().enableExternalizedCheckpoints(ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        // env
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        Configuration conf = new Configuration();
        conf.set(RestOptions.BIND_PORT, "8081,8089");
        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(conf);
        env.setParallelism(Integer.parseInt(parameters.get("env.parallelism.all","1")));
        // TODO local test
        env.getCheckpointConfig().setCheckpointStorage(parameters.get("checkpointDataUri","file:///C:\\Users\\<USER>\\Desktop\\Work\\CUBD\\Code\\checkpoints\\rts-tag"));

        // rocksDB
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM); // 设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);

        // checkpoint
        // TODO local test
        env.enableCheckpointing(TimeUnit.SECONDS.toMillis(parameters.getLong("env.ck.interval",30)));
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setMinPauseBetweenCheckpoints(TimeUnit.SECONDS.toMillis(parameters.getLong("env.ck.minPause",10)));
        checkpointConfig.setCheckpointTimeout(TimeUnit.SECONDS.toMillis(parameters.getLong("env.ck.timeout",30)));
        checkpointConfig.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // TODO local test
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(parameters.getInt("env.restart.times",10),
                Time.of(parameters.getLong("env.restart.interval.sec",10), TimeUnit.SECONDS))); // 尝试重启次数,重启间隔秒数

        // kafka配置文件
        Properties srcKafkaProp = new Properties();
        srcKafkaProp = ParameterTool.fromMap(parameters.toMap()).getProperties();
        srcKafkaProp.setProperty("group.id", srcKafkaProp.getProperty("group.id") + jobName);

        // // flink连接kafka
        // FlinkKafkaConsumer<byte[]> consumer = new FlinkKafkaConsumer<byte[]>(
        //         Arrays.asList(srcKafkaProp.getProperty("oggTopicInfo").split(",")),
        //         new ByteArraySchema(),
        //         srcKafkaProp);
        String brokerSever = parameters.get("source.bootstrap");
        String topic = parameters.get("source.topic","CB_ALL");

//        FlinkKafkaConsumer<byte[]> consumer = KafkaSource.getFlinkKafkaConsumer(brokerSever, topic, parameters);
//        if (Boolean.parseBoolean(parameters.get("isStartFromTimestamp"))) {
//            consumer.setStartFromTimestamp(Long.parseLong(parameters.get("startFromTimestamp")));
//        }
//
//        // 消费kafka数据
//        DataStreamSource<byte[]> srcData = env.addSource(consumer, "kafkaSrc")
//                .setParallelism(dataSourceParallelism);

        OutputTag<MajorBean> toHbaseTag = new OutputTag<MajorBean>("toHbase"){};
        // 解析kafka数据
        SingleOutputStreamOperator<MajorBean> msgStream =
                MyKafkaStream.addSource(env, parameters, topic, new MyDeserializationSchema())
                        .process(new ProcessFunction<MajorBean, MajorBean>() {
                            @Override
                            public void processElement(MajorBean value, Context ctx, Collector<MajorBean> out) throws Exception {
                                boolean b_TF_F_USER = "TF_F_USER".equals(value.getSourceJsonBean().getTable_name());
                                if (b_TF_F_USER) {
                                    out.collect(value);
                                    ctx.output(toHbaseTag,value);
                                }
                            }
                        });
//                .flatMap(new Mapper1(parameters)).uid("OggMsgProcessFunction").name("parseMsg")
//                        .filter(new FilterFunction<MajorBean>() {
//                            @Override
//                            public boolean filter(MajorBean majorBean) throws Exception {
//                                boolean b_TF_F_USER = "TF_F_USER".equals(majorBean.getSourceJsonBean().getTable_name());
//                                if (b_TF_F_USER) {
//                                    return true;
//                                }
//                                return false;
//                            }
//                        });
//                .filter(new OggMsgRichFilterFunction(tableName, initDate)).uid("OggMsgRichFilterFunction").name("filterMsg");

//        msgStream.print();
        SingleOutputStreamOperator<String> outStream = msgStream.getSideOutput(toHbaseTag).map(new OggMessage2UtProcessFunction(tableName, parameters)).uid("OggMessage2UtProcessFunction").name("MsgGenDt");
//        SingleOutputStreamOperator<String> outStream = msgStream.getSideOutput(toHbaseTag).map(new MapFunction<MajorBean, String>() {
//            @Override
//            public String map(MajorBean value) throws Exception {
//                return value.toString();
//            }
//        });
//        SideOutputDataStream<MajorBean> sideOutput = msgStream.iterate().getSideOutput(toHbaseTag);
        outStream.print();

        // paimon
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

        tableEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='table-store',\n" +
                "    'warehouse'='file:///C:/Users/<USER>/Desktop/Work/CUBD/Code/data/paimon/catalog',\n" +
//                "    'warehouse' = 'hdfs:///user/hh_slfn2_sschj_gray/paimon',\n" +
                "    'default-database' = 'ubd_sscj_gray_flink'\n" +
                ");");

        tableEnv.executeSql("use catalog paimon_catalog;");

        tableEnv.executeSql("CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_gray_flink`.`ods_r_paimon_cb_all_20230607_pufc`(\n" +
//                " id STRING PRIMARY KEY,\n" +
                " `system` STRING,\n" +
                " table_name STRING,\n" +
                " operation STRING,\n" +
                " SERIAL_NUMBER STRING,\n" +
                " USER_ID STRING,\n" +
                " PROVINCE_CODE STRING,\n" +
                " EPARCHY_CODE STRING,\n" +
                " REMOVE_TAG STRING,\n" +
                " OPEN_DATE STRING,\n" +
                " CREDIT_VALUE STRING,\n" +
                " USER_STATE_CODESET STRING,\n" +
                " PRODUCT_ID STRING,\n" +
                " NET_TYPE_CODE STRING,\n" +
//                " dt BIGINT\n" +
//                " event_time TIMESTAMP(3),\n" +
//                " kafka_time TIMESTAMP(3),\n" +
                " paimon_time TIMESTAMP(3),\n" +
                " pt STRING,\n" +
                " PRIMARY KEY (USER_ID,`system`,pt) NOT ENFORCED \n" +
                ") partitioned by (pt) with (\n" +
                " 'merge-engine' = 'partial-update',\n" +
                " 'sequence.field' = 'paimon_time',\n" +
                " 'changelog-producer' = 'full-compaction'\n" +
//                " 'bucket' = '2',\n" +
//                " 'bucket-key' = 'USER_ID',\n" +
//                " 'write-mode' = 'append-only' \n" +
//                " 'snapshot.time-retained' = '1h',\n " +
//                " 'write-only' = 'true'\n" +
                ");");

//        Table tableFromDS = tableEnv.fromDataStream(msgStream,
//                Schema.newBuilder()
//                        .column("system", "STRING")
//                        .column("table_name", "STRING")
//                        .column("operation", "STRING")
//                        .column("SERIAL_NUMBER", "STRING")
//                        .column("USER_ID", "STRING")
//                        .column("PROVINCE_CODE", "STRING")
//                        .column("EPARCHY_CODE", "STRING")
//                        .column("REMOVE_TAG", "STRING")
//                        .column("OPEN_DATE", "STRING")
//                        .column("CREDIT_VALUE", "STRING")
//                        .column("USER_STATE_CODESET", "STRING")
//                        .column("PRODUCT_ID", "STRING")
//                        .column("NET_TYPE_CODE", "STRING")
////                .column("f1", "DECIMAL(10, 2)")
////                .columnByExpression("c", "f1 - 1")
////                .column("f0", "STRING")
//                        .build());
//        tableEnv.createTemporaryView("tmpViewFromDS_20230531",msgStream);
        tableEnv.createTemporaryView("tmpViewFromDS_20230531",msgStream,
                Schema.newBuilder()
                .column("system", "STRING")
                .column("table_name", "STRING")
                .column("operation", "STRING")
                .column("SERIAL_NUMBER", "STRING")
                .column("USER_ID", "STRING")
                .column("PROVINCE_CODE", "STRING")
                .column("EPARCHY_CODE", "STRING")
                .column("REMOVE_TAG", "STRING")
                .column("OPEN_DATE", "STRING")
                .column("CREDIT_VALUE", "STRING")
                .column("USER_STATE_CODESET", "STRING")
                .column("PRODUCT_ID", "STRING")
                .column("NET_TYPE_CODE", "STRING")
//                .column("f1", "DECIMAL(10, 2)")
//                .columnByExpression("c", "f1 - 1")
//                .column("f0", "STRING")
                .build());


//        Table resTable = tableEnv.sqlQuery("SELECT * FROM tmpViewFromDS_20230531;");
//        tableEnv.toDataStream(resTable).print();

//        tableEnv.executeSql("INSERT INTO ods_r_paimon_cb_all_20230605_zym(`system`, table_name, operation, USER_ID, SERIAL_NUMBER, pt) values('sys1', 'TEST_TABLE1', 'I', 'uid1', 'sn1', '20230605');");

        tableEnv.executeSql("INSERT INTO `paimon_catalog`.`ubd_sscj_gray_flink`.`ods_r_paimon_cb_all_20230607_pufc` " +
                "select " +
                "`system`," +
                "table_name," +
                "operation," +
                "SERIAL_NUMBER," +
                "USER_ID," +
                "PROVINCE_CODE," +
                "EPARCHY_CODE," +
                "REMOVE_TAG," +
                "OPEN_DATE," +
                "CREDIT_VALUE," +
                "USER_STATE_CODESET," +
                "PRODUCT_ID," +
                "NET_TYPE_CODE," +
//                data_receive_time event_time,
//                data_receive_time kafka_time,
                "CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time," +
//                "DATE_FORMAT(CURRENT_TIMESTAMP as TIMESTAMP(3), 'yyyyMMdd') as pt\n" +
                "DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyyMMdd') as pt \n" +
                "from tmpViewFromDS_20230531;");
//        tableEnv.executeSql("INSERT INTO ods_r_paimon_cb_all_20230605_default select *,CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time,'20230605' as pt from tmpViewFromDS_20230531;");

//        tableEnv.executeSql("CREATE TEMPORARY TABLE IF NOT EXISTS print_table_20230531 (\n" +
////                " id STRING PRIMARY KEY,\n" +
//                " `system` STRING,\n" +
//                " table_name STRING,\n" +
//                " operation STRING,\n" +
//                " SERIAL_NUMBER STRING,\n" +
//                " USER_ID STRING,\n" +
//                " PROVINCE_CODE STRING,\n" +
//                " EPARCHY_CODE STRING,\n" +
//                " REMOVE_TAG STRING,\n" +
//                " OPEN_DATE STRING,\n" +
//                " CREDIT_VALUE STRING,\n" +
//                " USER_STATE_CODESET STRING,\n" +
//                " PRODUCT_ID STRING,\n" +
//                " NET_TYPE_CODE STRING,\n" +
////                " dt BIGINT\n" +
////                " event_time TIMESTAMP(3),\n" +
////                " kafka_time TIMESTAMP(3),\n" +
//                " paimon_time TIMESTAMP(3),\n" +
//                " pt STRING\n" +
//                ") WITH (\n" +
//                "  'connector' = 'print'\n" +
//                ");");
////
//        tableEnv.executeSql("insert into print_table_20230531 select * from ods_r_paimon_cb_all_20230605_default;");
//        tableEnv.executeSql("INSERT INTO print_table_20230531 " +
//                "select " +
//                "`system`," +
//                "table_name," +
//                "operation," +
//                "SERIAL_NUMBER," +
//                "USER_ID," +
//                "PROVINCE_CODE," +
//                "EPARCHY_CODE," +
//                "REMOVE_TAG," +
//                "OPEN_DATE," +
//                "CREDIT_VALUE," +
//                "USER_STATE_CODESET," +
//                "PRODUCT_ID," +
//                "NET_TYPE_CODE," +
////                data_receive_time event_time,
////                data_receive_time kafka_time,
////                "CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time," +
//                "paimon_time," +
////                "DATE_FORMAT(CURRENT_TIMESTAMP as TIMESTAMP(3), 'yyyyMMdd') as pt\n" +
////                "'20230531' as pt \n" +
//                "pt \n" +
//                "from ods_r_paimon_cb_all_20230531;");

        // convert to DataStream
//        Table table = tableEnv.sqlQuery("SELECT * FROM ods_r_paimon_cb_all_20230605_pufc");
////        DataStream<Row> dataStream = tableEnv.toDataStream(table);
//        DataStream<Row> dataStream = tableEnv.toChangelogStream(table);
//
//        // use this datastream
//        dataStream.executeAndCollect().forEachRemaining(System.out::println);
////        dataStream.print();

        // 处理标签
//        DataStream<String> msg2StringStream = msgStream
                // 根据数据生成标签插入hbase
//                .map(new OggMessage2UtProcessFunction(tableName,parameters)).uid("OggMessage2UtProcessFunction").name("MsgGenDt");

//        OutputFileConfig outputFileConfig = new OutputFileConfig("part", ".gz");

        // 准备数据写入hdfs
//        StreamingFileSink<String> oggMsgStreamingFileSinkForBulkFormat = StreamingFileSink
//                .forBulkFormat(homePath, new GzipBulkStringWriterFactory())
//                .withBucketAssigner(new MyDateTimeBucketAssigner<String>("yyyyMMdd", msgType+"_"+tableName))
//                .withRollingPolicy(OnCheckpointRollingPolicy.build())
//                .withBucketCheckInterval(10)
//                .withOutputFileConfig(outputFileConfig)
//                .build();


        // StreamingFileSink
        //         .forRowFormat(homePath, new SimpleStringEncoder<String>("UTF-8"))
        //         .withBucketAssigner(new MyDateTimeBucketAssigner<String>("yyyyMMdd", msgType+"_"+tableName))
        //         .withRollingPolicy(DefaultRollingPolicy
        //                                 .builder()
        //                                 .withMaxPartSize(1024 * 1024 * 1024L)
        //                                 .withInactivityInterval( 60 * 60 * 1000L )
        //                                 .withRolloverInterval( 24 * 60 * 60 * 1000L )
        //                                 .build())
        //         .withBucketCheckInterval(60*1000L)
        //         .withOutputFileConfig(outputFileConfig)
        //         .build(); 

//        msg2StringStream.addSink(oggMsgStreamingFileSinkForBulkFormat).uid("writeToHdfs").name("writeToHdfs");



        // exec
        env.disableOperatorChaining();
        // My-Optional-Args
//        env.execute(parameters.get("env.execute.name", "rts_tag_1_local"));
        env.execute(jobName);
        log.info("==============程序提交完毕=============");

    }

}
