package com.unicom.rts.integration.bean;

import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.MapTypeInfo;

import java.lang.reflect.Type;
import java.util.Map;
/**
 * <AUTHOR>
 * @Date 2023/4/6 19:21
 * @Description
 */
public class MapTypeInfoFactory extends TypeInfoFactory<Map<String, String>> {
    @Override
    @SuppressWarnings("unchecked")
    public TypeInformation<Map<String, String>> createTypeInfo(Type t, Map<String, TypeInformation<?>> genericParameters) {
        return new MapTypeInfo<>(BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO);
    }
}