package com.unicom.rts.integration.utils;

import java.util.Map;

public class MyUtil {
    public static void setSum(Map<String, Long> metricMap, String topic, String metricName, Long value) {
        String key = topic + ":" + metricName;
        Long curValue = metricMap.get(key);

        if (curValue == null) {
            metricMap.put(key, value);
        } else {
            metricMap.put(key, curValue + value);
        }
    }

    public static void setMaxValue(Map<String, Long> metricMap, String topic, String metricName, Long value) {
        String key = topic + ":" + metricName;
        Long curValue = metricMap.get(key);

        if (curValue == null || curValue < value) {
            metricMap.put(key, value);
        }
    }

    public static void setMinValue(Map<String, Long> metricMap, String topic, String metricName, Long value) {
        String key = topic + ":" + metricName;
        Long curValue = metricMap.get(key);

        if (curValue == null || curValue > value) {
            metricMap.put(key, value);
        }
    }

    public static String getHeader(Map<String, String> headers, String key) {
        Object value = headers.get(key);
        if (value == null) {
            return "";
        } else {
            return String.valueOf(value);
        }
    }

    public static boolean isLocal() {
        String osName = System.getProperties().getProperty("os.name");
        return osName.toLowerCase().contains("window");
    }
}
