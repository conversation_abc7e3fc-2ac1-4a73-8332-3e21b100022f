package com.unicom.rts.integration.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/1/3 18:17
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RuleBean {
    String op;
    int ruleId;
    String topicName;
    Long startTime;
    Long expireTime;
    int businessId;
    int userId;
    String provId;
    RuleParamBean param;
    String apiId;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        RuleBean that = (RuleBean) o;
        return ruleId == that.ruleId;
    }

    @Override
    public int hashCode() {
        return ruleId;
    }

    @Override
    public String toString() {
        return "RuleEntity{" +
                "op='" + op + '\'' +
                ", ruleId=" + ruleId +
                ", topicName='" + topicName + '\'' +
                ", startTime=" + startTime +
                ", expireTime=" + expireTime +
                ", businessId=" + businessId +
                ", userId=" + userId +
                ", provId='" + provId + '\'' +
                ", param=" + param +
                '}';
    }
}
