package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/7/14 10:56
 * @Description
 */
@Slf4j
public class BroadcastProcessTd2 extends KeyedBroadcastProcessFunction<String, Major<PERSON>ean, MajorBean, MajorBean> {
    ParameterTool conf;
    OutputTag<String> printSinkTag;
    String tableName;
    boolean isDiscnt;
    boolean isProduct;
    final MapStateDescriptor<String, String> tdStateTd2 = new MapStateDescriptor<>("tdStateTd2", Types.STRING, Types.STRING);
    public BroadcastProcessTd2(ParameterTool conf, OutputTag<String> ot) {
        this.conf = conf;
        printSinkTag = ot;
        tableName = conf.get("tableName", "tf_f_user_sp");
        isDiscnt = "tf_f_user_discnt".equalsIgnoreCase(tableName);
        isProduct = "tf_f_user_product".equalsIgnoreCase(tableName);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(MajorBean value, ReadOnlyContext ctx, Collector<MajorBean> out) throws Exception {
        ReadOnlyBroadcastState<String, String> broadcastState = ctx.getBroadcastState(tdStateTd2);
        if (isProduct) {
            if(broadcastState.contains(value.getPRODUCT_ID())) {
                value.setMAIN_PRODUCT_MONTHLY_FEE(broadcastState.get(value.getPRODUCT_ID()));
            }
        }
        out.collect(value);
//        String res = "print="+System.currentTimeMillis()+"||source="+value.getDataSource()+"||uid="+value.getUserId()+"||productid="+product_id_hashset.toString()+"||sn="+value.getSn()+"||prov="+value.getPROVINCE_CODE();
//        log.info("MY-INFO-BroadcastProcess-"+res);
    }

    @Override
    public void processBroadcastElement(MajorBean value, Context ctx, Collector<MajorBean> out) throws Exception {
        BroadcastState<String, String> broadcastState = ctx.getBroadcastState(tdStateTd2);
        long currentTimeMillis = System.currentTimeMillis();
        if (isProduct) {
            String product_id = value.getPRODUCT_ID();
            if ("Delete".equalsIgnoreCase(value.getOpt()) || value.getRowKind().equals(RowKind.DELETE)) {
                broadcastState.remove(product_id);
            } else {
                String product_mode = value.getPRODUCT_MODE();
                if (value.getEnd_date_long() > currentTimeMillis && "00".equals(product_mode)) {
                    broadcastState.put(product_id, value.getRSRV_VALUE2());
                } else {
                    broadcastState.remove(product_id);
                }
            }
        }
    }
}
