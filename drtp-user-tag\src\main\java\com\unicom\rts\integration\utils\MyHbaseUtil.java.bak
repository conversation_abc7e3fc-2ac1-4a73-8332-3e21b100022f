package com.unicom.rts.integration.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.PrefixFilter;
import org.apache.hadoop.hbase.security.User;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/3 20:47
 * @Description
 */
public class MyHbaseUtil {

    private final static Logger log = LoggerFactory.getLogger(MyHbaseUtil.class);

    private Configuration conf = null;
    private UserGroupInformation ugi = null;
    private Connection connection = null;
    private Admin admin = null;
    private HTable hTable = null;
    private static String hbaseEncode = "utf8";// 编码格式
    private Configuration hbaseConfig = new Configuration();
    private String user="hh_per_poc_000_percj";
    private String zkHosts="*************,*************,*************";
    private String zkNodeParent="/hbase-unsecure";
    private String zkPort="2181";

    public MyHbaseUtil(String user,String zkHosts,String zkNodeParent,String zkPort){
        this.user = user;
        this.zkHosts = zkHosts;
        this.zkNodeParent = zkNodeParent;
        this.zkPort = zkPort;
    }

    public Admin getAdmin() {
        return admin;
    }

    public Configuration getHbaseConfig() {
        return hbaseConfig;
    }

    public void setHbaseConfig(Configuration hbaseConfig) {
        this.hbaseConfig = hbaseConfig;
    }

    public void init() {
        hbaseConfig.set("hbase.zookeeper.quorum", zkHosts);
        hbaseConfig.set("hbase.zookeeper.property.clientPort", zkPort);
        hbaseConfig.set("zookeeper.znode.parent", zkNodeParent);

        conf = HBaseConfiguration.create(hbaseConfig);
        try {
            UserGroupInformation userGroupInformation = UserGroupInformation.createRemoteUser(user);
            connection = ConnectionFactory.createConnection(conf, User.create(userGroupInformation));
            log.info("成功连接Hbase {}" + conf.toString());
        } catch (IOException e) {
            log.error("获取Hbase连接异常，"+ e.toString());
        }
    }

    public HTable getTable(String tableName) {

        try {
            return (HTable) connection.getTable(TableName.valueOf(tableName));
        } catch (IOException e) {
            log.error("IOException:{}" , e);
        }

        return null;

    }

    // 放在execInSafeMode使用此方法
    public Result getResult(HTable hTable, Get get) {

        if (StringUtils.isEmpty(hTable.getName().toString()) || null==get) {
            return null;
        }
        try {
            return hTable.get(get);
        } catch (IOException e) {
            log.error("IOException:{}" , e);
        }
        return null;
    }

    // 放在execInSafeMode使用此方法
    public void put(HTable hTable, Put put) {
        try {
            hTable.put(put);
        } catch (IOException e) {
            log.error(e.toString());
        }
    }
    // 放在execInSafeMode使用此方法
    public void put(HTable hTable, List<Put> puts) {
        try {
            hTable.put(puts);
        } catch (IOException e) {
            log.error(e.toString());
        }
    }

}

