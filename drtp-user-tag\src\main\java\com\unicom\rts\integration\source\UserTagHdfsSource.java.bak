package com.unicom.rts.integration.source;

import com.unicom.realtime.bean.MajorBean;
import com.unicom.realtime.bean.UserTagBean;
import com.unicom.realtime.enums.UserTagEnum;
import com.unicom.realtime.utils.MyFileSystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.RichParallelSourceFunction;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * @Date 2023/2/15 21:32
 * @Description
 */
public class UserTagHdfsSource extends RichParallelSourceFunction<MajorBean> implements CheckpointedFunction {

    private final static Logger logger = LoggerFactory.getLogger(UserTagHdfsSource.class);
    private ParameterTool conf;
    private String usertagPath;
    private FileSystem fs;
    private boolean isRunning;
    private boolean isRead = false;
    private Long lastTime = 0L;
    ListState<Long> userTagLastUpdateTimeState;

    public UserTagHdfsSource(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        usertagPath = conf.get("source.usertag.path","hdfs:///warehouse/tablespace/external/hive/ubd_sscj_gray_tag.db/dwa_d_user_tag");
        MyFileSystemUtil fsu = new MyFileSystemUtil(conf);
        fs = fsu.getFS();
        isRunning = true;
        logger.info("MY-INFO-==========成功获取fs=========");
    }

    @Override
    public void run(SourceContext<MajorBean> sourceContext) throws Exception {
        readUsertagHdfsFile(sourceContext);
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    private void readUsertagHdfsFile(SourceContext<MajorBean> sourceContext) {
        int parallelism = getRuntimeContext().getNumberOfParallelSubtasks();
        int subTaskId = getRuntimeContext().getIndexOfThisSubtask();
        while (isRunning) {
            FileStatus[] status = new FileStatus[0];
            try {
                status = fs.listStatus(new Path(usertagPath));
            } catch (IOException e) {
                logger.error("MY-ERROR-fs.listStatus IOException", e);
            }
            logger.info("MY-INFO-get usertag files");
            if (status.length == 0) {
                logger.error("MY-ERROR-not find file");
            }
            if (status.length > 1) {
                logger.info("MY-INFO-multi files size {}", status.length);
            }
            for (FileStatus file : status) {
                if ("SUCCESS".equals(file.getPath().getName())) {
                    //获取success文件更新时间
                    Long successTime = file.getModificationTime();
                    //success文件更新时间大于当前时间
                    isRead = lastTime < successTime;
                    logger.info("MY-INFO-lastTime{} successTime {} isRead {}", lastTime, successTime, isRead);
                }
            }
            if (isRead) {
                lastTime = System.currentTimeMillis();
                for (FileStatus file : status) {
                    int fileHashCode = file.getPath().getName().hashCode() & Integer.MAX_VALUE;
                    if (!"SUCCESS".equals(file.getPath().getName()) && fileHashCode % parallelism == subTaskId) {
                        logger.info("MY-INFO-parallelism {} subtask {} read file {}", parallelism, subTaskId, file.getPath().getName());
                        try (BufferedReader br = new BufferedReader(new InputStreamReader(fs.open(file.getPath())))) {
                            String userTagData = "";
                            while ((userTagData = br.readLine()) != null) {
                                parseUserTag(userTagData, sourceContext);
                            }
                        } catch (IOException e) {
                            logger.error("MY-ERROR-read HDFS file IOException", e);
                        }
                    }
                }
                logger.info("MY-INFO-HdfsReadComplete");
            }
            int updateTime = conf.getInt("update.usertag.file.time",60);
            try {
                sleep(updateTime * 60 * 1000);
            } catch (InterruptedException e) {
                logger.error("MY-ERROR-InterruptedException", e);
            }
        }
    }

    private void parseUserTag(String userTagStr, SourceContext<MajorBean> ctx) {
        String[] columns = StringUtils.splitPreserveAllTokens(userTagStr, "\001");
        UserTagBean userTag = new UserTagBean();
        userTag.setK002902(columns[33]);
//        userTag.setK000001(columns[UserTagEnum.K000001.ordinal()]);
//        userTag.setK000002(columns[UserTagEnum.K000002.ordinal()]);
//        userTag.setK000004(columns[UserTagEnum.K000004.ordinal()]);
//        userTag.setK000006(columns[UserTagEnum.K000006.ordinal()]);
//        userTag.setK000046(columns[UserTagEnum.K000046.ordinal()]);
//        userTag.setK000055(columns[UserTagEnum.K000055.ordinal()]);
//        userTag.setK000056(columns[UserTagEnum.K000056.ordinal()]);
//        userTag.setK000057(columns[UserTagEnum.K000057.ordinal()]);
//        userTag.setK000058(columns[UserTagEnum.K000058.ordinal()]);
//        userTag.setK000060(columns[UserTagEnum.K000060.ordinal()]);
//        userTag.setK000061(columns[UserTagEnum.K000061.ordinal()]);
//        userTag.setK000062(columns[UserTagEnum.K000062.ordinal()]);
//        userTag.setK000064(columns[UserTagEnum.K000064.ordinal()]);
//        userTag.setK000065(columns[UserTagEnum.K000065.ordinal()]);
//        userTag.setK000066(columns[UserTagEnum.K000066.ordinal()]);
//        userTag.setK003681(columns[UserTagEnum.K003681.ordinal()]);
//        userTag.setK003711(columns[UserTagEnum.K003711.ordinal()]);
//        userTag.setK002436(columns[UserTagEnum.K002436.ordinal()]);
        MajorBean majorBean = new MajorBean();
        majorBean.setDataSource("UserTagHdfsFile");
        majorBean.setSn(columns[UserTagEnum.DeviceNumber.ordinal()]);
        majorBean.setUserTagBean(userTag);
        ctx.collect(majorBean);
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        if (!isRunning) {
            logger.info("snapshotState() called on closed UserTagHdfsSource source");
        } else {
            userTagLastUpdateTimeState.clear();
            userTagLastUpdateTimeState.add(lastTime);
        }
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        ListStateDescriptor<Long> userTagLastUpdateTimeDes = new ListStateDescriptor<>("userTagLastUpdateTime", Types.LONG);
        userTagLastUpdateTimeState = context.getOperatorStateStore().getListState(userTagLastUpdateTimeDes);
        if (context.isRestored()) {
            for (Long restoreTime : userTagLastUpdateTimeState.get()) {
                lastTime = restoreTime > lastTime ? restoreTime : lastTime;
            }
            logger.info(
                    "UserTagHdfsSource subtask {} restored state: {}.",
                    getRuntimeContext().getIndexOfThisSubtask(),
                    lastTime);
        } else {
            logger.info(
                    "UserTagHdfsSource subtask {} has no restore state.",
                    getRuntimeContext().getIndexOfThisSubtask());
        }
    }
}
