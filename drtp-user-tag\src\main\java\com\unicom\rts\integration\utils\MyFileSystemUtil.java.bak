package com.unicom.rts.integration.utils;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/2/15 21:34
 * @Description
 */
public class MyFileSystemUtil {
    private static final Logger logger = LoggerFactory.getLogger(MyFileSystemUtil.class);

    private ParameterTool conf;

    public MyFileSystemUtil(ParameterTool conf) {
        this.conf = conf;
    }

    public FileSystem getFS() throws Exception {

        Configuration config = new Configuration();

//        config.set("fs.default.name", conf.get("hdfs.host","hdfs://**************:8020"));// namenode的地址和端口
//        config.set("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem");
//        config.set("dfs.client.use.datanode.hostname", "true");
//        config.set("dfs.socket.timeout", "30000");

//        UserGroupInformation.setConfiguration(config);

        FileSystem fs = FileSystem.get(config); // 读取配置文件
        fs.getStatus();
        return fs;
    }
}
