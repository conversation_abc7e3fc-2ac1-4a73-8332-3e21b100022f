package com.unicom.rts.integration.utils;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.streaming.connectors.kafka.partitioner.FlinkKafkaPartitioner;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/11/14 14:16
 */
public class MultiTopicSerializationSchema implements KafkaRecordSerializationSchema<Tuple3<String, String, Iterable<Header>>> {
    @Nullable
    private final FlinkKafkaPartitioner<Tuple3<String, String, Iterable<Header>>> partitioner;
    @Nullable
    private final SerializationSchema<Tuple3<String, String, Iterable<Header>>> keySerialization;
    @Nullable
    private final SerializationSchema<Tuple3<String, String, Iterable<Header>>> valueSerialization;

    public MultiTopicSerializationSchema(
            @Nullable FlinkKafkaPartitioner<Tuple3<String, String, Iterable<Header>>> partitioner,
            @Nullable SerializationSchema<Tuple3<String, String, Iterable<Header>>> keySerialization,
            @Nullable SerializationSchema<Tuple3<String, String, Iterable<Header>>> valueSerialization) {
        this.partitioner = partitioner;
        this.keySerialization = keySerialization;
        this.valueSerialization = valueSerialization;
    }

    public MultiTopicSerializationSchema() {
        this(null, null, null);
    }

    @Override
    public void open(SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext) throws Exception {
        if (keySerialization != null) {
            keySerialization.open(context);
        }
        if (partitioner != null) {
            partitioner.open(
                    sinkContext.getParallelInstanceId(),
                    sinkContext.getNumberOfParallelInstances());
        }
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(Tuple3<String, String, Iterable<Header>> element, KafkaSinkContext context, Long timestamp) {
        String targetTopic = element.f0;
        Iterable<Header> headers = element.f2;

        final byte[] keySerialized;
        if (keySerialization == null) {
            keySerialized = null;
        } else {
            keySerialized = keySerialization.serialize(element);
        }

        final byte[] valueSerialized;
        if (valueSerialization == null) {
            valueSerialized = element.f1.getBytes(StandardCharsets.UTF_8);
        } else {
            valueSerialized = valueSerialization.serialize(element);
        }

        return new ProducerRecord<>(
                targetTopic,
                extractPartition(element, keySerialized, valueSerialized, context.getPartitionsForTopic(targetTopic), targetTopic),
                null,
                keySerialized,
                valueSerialized,
                headers);
    }

    private Integer extractPartition(
            Tuple3<String, String, Iterable<Header>> element,
            @Nullable byte[] keySerialized,
            byte[] valueSerialized,
            int[] partitions,
            String topic) {
        if (partitioner != null) {
            return partitioner.partition(
                    element, keySerialized, valueSerialized, topic, partitions);
        }
        return null;
    }
}
