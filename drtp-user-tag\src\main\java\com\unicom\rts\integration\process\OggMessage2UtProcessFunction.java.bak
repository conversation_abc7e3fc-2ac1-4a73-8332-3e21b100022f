package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

public class OggMessage2UtProcessFunction extends RichMapFunction<MajorBean, String> {

	private Processor<MajorBean> processor;
	private ProcessFactory<Processor<MajorBean>> processorFactory;
	private String tableName;
	private ParameterTool config;

	public OggMessage2UtProcessFunction(String tableName, ParameterTool config){
		this.tableName = tableName;
		this.config = config;
	}
	
	@Override
	public void open(Configuration parameters){
		processorFactory = new OggMsgProcessorFactory(config);
		processor = processorFactory.createProcessor(tableName);
	}
	
	@Override
	public String map(MajorBean value) throws IllegalAccessException {
		
		return processor.process(value);
	}

}
