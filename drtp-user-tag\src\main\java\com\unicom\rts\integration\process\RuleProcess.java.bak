package com.unicom.rts.integration.function;

//import com.google.common.collect.HashMultimap;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.unicom.realtime.bean.MajorBean;
import com.unicom.realtime.bean.RuleBean;
import com.unicom.realtime.bean.UserTagBean;
import com.unicom.realtime.utils.MyRedisPool;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple4;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hudi.org.apache.hbase.thirdparty.com.google.common.collect.HashMultimap;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisCluster;

import javax.script.ScriptException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2023/1/4 19:47
 * @Description
 */
public class RuleProcess extends KeyedBroadcastProcessFunction<String, MajorBean, List<RuleBean>, Tuple4<String, MajorBean, Iterable<Header>, Integer>> {
    private static HashMap<String, String> attrRelation = new HashMap<>();
    private final static Logger logger = LoggerFactory.getLogger(RuleProcess.class);
    private String[] columns = ("off_k000001,off_k000002,off_k000004,off_k000006,off_k000046,off_k000055,off_k000056,off_k000057,off_k000058,off_k000060,off_k000061,off_k000062,off_k000064,off_k000065,off_k000066,off_k003681,off_k003711,off_k002436").split(",");
    ParameterTool conf;
//    private Connection connection = null;
//    private HTable table = null;

//    private FileSystem fs;
    private boolean isRead=false;
    private Long lastTime = 0L;
    private HashMultimap<String, RuleBean> provIdRuleMap;
    HashMap<Integer, RuleBean> RuleBeanHashMap = null;
    private int misDataCnt=0;
    private ValueState<UserTagBean> userTagState;
    //初始化redis链接
    JedisCluster jedisCluster = null;
    

    public RuleProcess(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.relationMap();

        //初始本地归属省份-规则集合
        provIdRuleMap = HashMultimap.create();
        //初始化redis链接
        jedisCluster = MyRedisPool.getJedisPool(conf);
        //用户标签表状态
        //状态ttl
//        StateTtlConfig userTagTtlConfig = StateTtlConfig
//                .newBuilder(Time.days(5))
//                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
//                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
//                .cleanupInRocksdbCompactFilter(6000)
//                .build();
        ValueStateDescriptor<UserTagBean> userTagStateDes = new ValueStateDescriptor<>("userTagState", UserTagBean.class);
//        userTagStateDes.enableTimeToLive(userTagTtlConfig);
        userTagState = getRuntimeContext().getState(userTagStateDes);

    }

    @Override
    public void processElement(MajorBean data, ReadOnlyContext readOnlyContext, Collector<Tuple4<String, MajorBean, Iterable<Header>, Integer>> collector) throws Exception {

//        if ("UserTagHdfsFile".equals(data.getDataSource())) {
//            userTagState.update(data.getUserTagBean());
//            return;
//        }
//        if ("bus".equals(data.getDataSource())) {
//            if (null == userTagState.value()) {
//                userTagState.update(data.getUserTagBean());
//            } else {
//                UserTagBean userTagBus = userTagState.value();
//                userTagBus.setUserIdBus(data.getUserTagBean().getUserIdBus());
//                userTagState.update(userTagBus);
//            }
//            return;
//        }

        UserTagBean hudiUserTag = data.getUserTagBean();
        // 更新标签状态
        if (null != hudiUserTag) {
            if (null != hudiUserTag.getProvId() && null != hudiUserTag.getEparchyCode() && null != hudiUserTag.getCityCode()) {
                userTagState.update(hudiUserTag);
                return;
            } else {
                UserTagBean userTag = userTagState.value();
                if (userTag == null) {
                    userTagState.update(hudiUserTag);
                    return;
                }
                BeanUtil.copyProperties(hudiUserTag, userTag, CopyOptions.create().setIgnoreNullValue(true));
                userTagState.update(userTag);
                return;
            }
        }
//        if ("hudi".equals(data.getDataSource())) {
//            UserTagBean stateValue = userTagState.value();
//            if (null == stateValue) {
//                UserTagBean userTagBean = new UserTagBean();
//                userTagBean.setField0(data.getField0());
//                userTagBean.setField1(data.getField1());
//                userTagBean.setField2(data.getField2());
//                userTagBean.setField3(data.getField3());
//                userTagBean.setField4(data.getField4());
//                userTagBean.setField5(data.getField5());
//                userTagState.update(userTagBean);
//            } else {
//                stateValue.setField0(data.getField0());
//                stateValue.setField1(data.getField1());
//                stateValue.setField2(data.getField2());
//                stateValue.setField3(data.getField3());
//                stateValue.setField4(data.getField4());
//                stateValue.setField5(data.getField5());
//                userTagState.update(stateValue);
//            }
//            return;
//        }
        String hProvId=data.getProvReg();
        String areaId=data.getCityReg();

        boolean isGetUserTag = false;
        //查询状态，返回用户标签实体类
        UserTagBean userTag = userTagState.value();
        //1.返回不为空，则关联上了2.返回为空则没关联上,continue
//        data.setUserTagBean(userTag);
        if (userTag != null) {
//            if (StringUtils.isNotBlank(data.getUserTagBean().getField3())) {
                data.setCityRegFromTag(userTag.getEparchyCode());
//            }
//            data.setRt_b_user_id(data.getUserTagBean().getField1());
            data.setRt_b_city_code(userTag.getCityCode());
        }

        long outTime = System.currentTimeMillis();
        data.setRelease_time(String.valueOf(outTime));
//        List<Header> headers = new ArrayList<>();
//        headers.add(new RecordHeader("receive_time", null == data.getReceive_time() ? null : data.getReceive_time().getBytes()));
//        headers.add(new RecordHeader("root_time", null == data.getRoot_time() ? null : data.getRoot_time().getBytes()));
//        headers.add(new RecordHeader("integration_time", null == data.getIntegration_time() ? null : data.getIntegration_time().getBytes()));
//        headers.add(new RecordHeader("process_time", null == data.getProcess_time() ? null : data.getProcess_time().getBytes()));
//        headers.add(new RecordHeader("scene_id", null == data.getScene_id() ? null : data.getScene_id().getBytes()));
//        headers.add(new RecordHeader("event_time", null == data.getEvent_time() ? null : data.getEvent_time().getBytes()));
//        headers.add(new RecordHeader("event_province", null == data.getProvEvt() ? null : data.getProvEvt().getBytes()));
//        headers.add(new RecordHeader("owner_province", null == data.getProvReg() ? null : data.getProvReg().getBytes()));
//        headers.add(new RecordHeader("release_time", String.valueOf(outTime).getBytes()));
//        List<Header> headers2= new ArrayList<>();

        if (hProvId!= null && !"".equals(hProvId)) {

            // 获取该省分所有规则
            Set<RuleBean> ruleEntities = provIdRuleMap.get(hProvId);
            // 获取全国规则（如有）（不选择归属省份时，匹配全国）
            Set<RuleBean> ruleEntitiesWithAll = provIdRuleMap.get("099");
            if(!ruleEntitiesWithAll.isEmpty()) {
                ruleEntities.addAll(ruleEntitiesWithAll);
            }

            if (!ruleEntities.isEmpty()) {
                // 循环遍历该省下所有规则
                for (RuleBean ruleBean : ruleEntities) {
                    // 归属省分
                    if (ruleBean.getParam().getHProvId()!=null && !ruleBean.getParam().getHProvId().isEmpty()) {
                        if (!ruleBean.getParam().getHProvId().contains(hProvId)) {
                            continue;
                        }
                    }
                    // 归属地市
                    if (ruleBean.getParam().getHAreaIds()!=null && !ruleBean.getParam().getHAreaIds().isEmpty()) {
                        if (!ruleBean.getParam().getHAreaIds().contains(data.getCityReg())) {
                            continue;
                        }
                    }
                    // 业务处理
                    // roamType
                    List<Header> headersNow;
                    switch (ruleBean.getBusinessId()) {
                        // TODO 更新业务逻辑
                        case 50:
                            // 场景个性化业务过滤
                            if (StringUtils.isNotBlank(ruleBean.getParam().getDurationThreshold()) && StringUtils.isNotBlank(ruleBean.getParam().getDurationRelation())) {
//                                String REGEX = "[^0-9]";
                                String REGEX = "[^\\d+(\\.\\d*)?]";
                                double usedFlow = Double.parseDouble(Pattern.compile(REGEX).matcher(data.getSourceJsonBean().getUsed_flow()).replaceAll("").trim());
                                double limitFlow = Double.parseDouble(Pattern.compile(REGEX).matcher(data.getSourceJsonBean().getLimit_flow()).replaceAll("").trim());
                                double flowSatu = usedFlow / limitFlow;
                                data.setVelolimitingFlowSaturation(flowSatu);
                                boolean speedBool = compareAttrListMatch(String.valueOf(flowSatu), ruleBean.getParam().getDurationRelation(), ruleBean.getParam().getDurationThreshold(), "maxSpeed");
                                if (!speedBool){
                                    continue;
                                }
                            }
//                            headersNow=headers;
                            break;
                        default:
                            logger.error("MY-ERROR-RuleProcess switch default!!! business id="+ ruleBean.getBusinessId());
                            continue;
                    }


                    // 围栏判断
//                    if (null != ruleBean.getParam().getTradeAreaIds() && !ruleBean.getParam().getTradeAreaIds().isEmpty()) {
//                        boolean locFlg = false;
//                        for (Integer tradeId : data.getTradeIds()) {
//                            if (ruleBean.getParam().getTradeAreaIds().contains(tradeId)) {
//                                locFlg = true;
//                                break;
//                            }
//                        }
//                        if (!locFlg) {
//                            continue;
//                        }
//                    } else {
//                        if (null != ruleBean.getParam().getAreaProvId() && !ruleBean.getParam().getAreaProvId().isEmpty()) {
//                            if (!ruleBean.getParam().getAreaProvId().contains(data.getProvEvt())) {continue;}
//                        }
//                        if (null != ruleBean.getParam().getAreaCityId() && !ruleBean.getParam().getAreaCityId().isEmpty()) {
//                            if (!ruleBean.getParam().getAreaCityId().contains(data.getCityEvt())) {continue;}
//                        }
//                    }

                    // 群组判断
//                    if (ruleBean.getParam().getGroupId() != 0) {
//                        if (!UserGroupComputerProcess.isInUserGroup(jedisCluster, ruleBean.getParam().getGroupId() + "", data.getSn(), ruleBean.getParam().getGroupType())) {
//                            continue;
//                        }
//                    }

                    // 输出
//                    collector.collect(new Tuple4<>(ruleBean.getTopicName(), data, headersNow, ruleBean.getBusinessId()));
                    collector.collect(new Tuple4<>(ruleBean.getTopicName(), data, data.getHeaders(), ruleBean.getBusinessId()));
                    // 侧输出
//                    readOnlyContext.output();
                }
            }
        }

    }

    @Override
    public void processBroadcastElement(List<RuleBean> ruleEntities,Context
            context, Collector<Tuple4<String, MajorBean, Iterable<Header>, Integer>> collector) throws Exception {

        HashMultimap<String, RuleBean> tmpTradeRuleMap = HashMultimap.create();

        for (RuleBean rule : ruleEntities) {
            if(rule.getParam().getHProvId()!=null && rule.getParam().getHProvId().size()!=0) {
                rule.getParam().getHProvId().forEach(
                        provId -> tmpTradeRuleMap.put(provId, rule));
            }else {
                tmpTradeRuleMap.put("099", rule);
            }
        }
        provIdRuleMap = tmpTradeRuleMap;
//        System.out.println("RULEMAP="+provIdRuleMap.toString());
//        logger.info("MY-INFO-RULEMAP="+provIdRuleMap.toString());

    }


    @Override
    public void close() throws Exception {
//        if (table != null) {
//            table.close();
//            connection.close();
//        }
    }

    private boolean compareAttrListMatch(String accData, String loGic, String fiGures, String attrCode) throws IOException, InterruptedException, ScriptException {
        if(loGic==null || attrCode==null || fiGures==null) {
            return false;
        }
        //012345
        if("0".equals(loGic) ||"1".equals(loGic) || "2".equals(loGic) || "3".equals(loGic) || "4".equals(loGic) || "5".equals(loGic)) {
            if((fiGures!=null && !"".equals(fiGures) ) &&  (attrRelation!=null && !attrRelation.isEmpty())) {
                String relationFinal = "";
                if(attrRelation.get(loGic)!=null && !"".equals(attrRelation.get(loGic))) {
                    relationFinal = attrRelation.get(loGic);
                }else {
                    return false;
                }
//                System.out.println("012345"+String.valueOf(attrCode + relationFinal + compareAttrValue));
                boolean isMatch = this.stringToBoolean(attrCode,accData,String.valueOf(attrCode + relationFinal + fiGures));
                if(!isMatch) {
                    return false;
                }else {
                    return true;
                }
            }else {
                return false;
            }
        }
        //11
        else if("6".equals(loGic)|| "7".equals(loGic)|| "8".equals(loGic)|| "9".equals(loGic)){
            if((fiGures!=null && !"".equals(fiGures) )&& fiGures.contains(",") && (attrRelation!=null && !"".equals(attrRelation) && !attrRelation.isEmpty())) {
                String firstcompareAttrValue = fiGures.split(",")[0];
                String secondcompareAttrValue = fiGures.split(",")[1];
                String firstRelationFinal = "";
                String secondRelationFinal = "";
                if(attrRelation.get(loGic)!=null && !"".equals(attrRelation.get(loGic)) && attrRelation.get(loGic).contains(",")) {
                    firstRelationFinal = attrRelation.get(loGic).split(",")[0];
                    secondRelationFinal = attrRelation.get(loGic).split(",")[1];
                }
                if("".equals(firstRelationFinal)) {
                    return false;
                }
                if("".equals(secondRelationFinal)) {
                    return false;
                }
                StringBuilder firstMatchStr = new StringBuilder();
                StringBuilder secondMatchStr = new StringBuilder();
                firstMatchStr.append(attrCode).append(firstRelationFinal).append(firstcompareAttrValue);
                secondMatchStr.append(attrCode).append(secondRelationFinal).append(secondcompareAttrValue);
//            System.out.println("6789====>firstMatchStr"+firstMatchStr);
//            System.out.println("6789====>secondMatchStr"+secondMatchStr);
                boolean firstMatch = this.stringToBoolean(attrCode, accData, String.valueOf(firstMatchStr));
                boolean secondMatch = this.stringToBoolean(attrCode, accData, String.valueOf(secondMatchStr));
                return firstMatch && secondMatch;
            }else {
                return false;
            }
        }else {
            return false;
        }
    }

    /**
     * string类型的表达式转化成布尔类型
     * @param attrName  入参的字段名称
     * @param attrsourceData 入参该字段的下发数据值
     * @param str 传入的字符串表达式
     * @return
     * @throws ScriptException
     */
    public boolean stringToBoolean(String attrName,String attrsourceData,String str) throws ScriptException {
//        System.out.println("stringToBoolean=====>"+attrName+"==>"+attrsourceData+"==>"+str);
        Expression compile = null;
        if(str!=null && !"".equals(str) && (str.contains("==")||str.contains(">")||str.contains("<")||str.contains(">=")||str.contains("<=")||str.contains("!="))) {
            compile = AviatorEvaluator.compile(str,true);
        }else {
            return false;
        }
        Map<String, Object> map = new HashMap<>();
        if(attrsourceData !=null && !"".equals(attrsourceData) && attrName !=null && !"".equals(attrName) && str.contains(attrName)&& !"null".equals(attrsourceData) && !"\\N".equals(attrsourceData)&& !"N".equals(attrsourceData)) {
            double data;
            try {
                data= Double.parseDouble(attrsourceData);
                // 20221121按郭永红要求修改为绝对值
                data=Math.abs(data);
            } catch (Exception e) {
                System.out.println("MY-ERROR-ChangeValueParserError=" + e.toString());
                logger.error("MY-ERROR-ChangeValueParserError = {}", e.toString());
                return false;
            }
            map.put(attrName, data);
            return (Boolean)compile.execute(map);
        }else {
            return false;
        }
    }

    private void relationMap() {
        attrRelation.put("0", "==");
        attrRelation.put("1", ">");
        attrRelation.put("2", "<");
        attrRelation.put("3", ">=");
        attrRelation.put("4", "<=");
        attrRelation.put("5", "!=");
        attrRelation.put("6", ">,<");
        attrRelation.put("7", ">=,<");
        attrRelation.put("8", ">,<=");
        attrRelation.put("9", ">=,<=");
    }

}
