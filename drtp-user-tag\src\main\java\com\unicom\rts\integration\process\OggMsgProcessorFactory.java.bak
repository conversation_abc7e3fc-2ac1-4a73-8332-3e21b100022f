package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import com.unicom.rts.integration.utils.MyHbaseUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OggMsgProcessorFactory implements ProcessFactory<Processor<MajorBean>> {

	private final static Logger log = LoggerFactory.getLogger(OggMsgProcessorFactory.class);
	private MyHbaseUtil hbaseUtil;
	private ParameterTool config;
	private String user;
	private String zkHosts;
	private String zkNodeParent;
	private String zkPort;
	
	public OggMsgProcessorFactory(ParameterTool config){
		this.config = config;
		//获取连接参数
		this.user = config.get("hbase.gch.user");
		this.zkHosts = config.get("hbase.gch.zkHosts");
		this.zkNodeParent = config.get("hbase.gch.zkNodeParent");
		this.zkPort = config.get("hbase.gch.zkPort");
		
		// 初始化hbase连接
//		hbaseUtil = new MyHbaseUtil(user,zkHosts,zkNodeParent,zkPort);
//		hbaseUtil.init();
	}

	@Override
	public Processor<MajorBean> createProcessor(String msgTableName) {
		// TODO Auto-generated method stub

		if(msgTableName.equalsIgnoreCase("tf_f_user")){
			return new TFUserProcessor(config);
//		}else if(msgTableName.equalsIgnoreCase("tf_f_user_item")){
//			return new TFUserItemProcessor(config);
//		}else if(msgTableName.equalsIgnoreCase("tf_f_user_product")){
//			return new TFUserPorductProcessor(config);
		}else {
			log.info(msgTableName+"未定义处理逻辑！");
			return null;
		}
	}

}
