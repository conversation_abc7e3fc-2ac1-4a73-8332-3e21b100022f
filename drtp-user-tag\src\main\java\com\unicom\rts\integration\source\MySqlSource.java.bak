package com.unicom.rts.integration.source;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unicom.realtime.bean.RuleBean;
import com.unicom.realtime.bean.RuleParamBean;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/3 18:28
 * @Description
 */
public class MySqlSource extends RichSourceFunction<List<RuleBean>> {

    private final static Logger logger = LoggerFactory.getLogger(MySqlSource.class);
    private PreparedStatement ps;
    private Connection connection;
    private volatile boolean isRunning = true;
    private ParameterTool conf;

    public MySqlSource(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        connection = getConnection(conf.get("jdbcUrl","jdbc:mysql://***********:18081/rts_db_gray?useUnicode=true&characterEncoding=UTF-8"), conf.get("mysql.user","gray_rts_db"), conf.get("mysql.password","GrayRTS@2022"));
        System.out.println("mysql-connection="+connection);
        // TODO business_id
        String sql = "select * from rule_submit_info " +
                "where unix_timestamp(start_time) <= unix_timestamp(NOW()) " +
                "and (expire_time is null or unix_timestamp(expire_time) >= unix_timestamp(NOW())) and business_id in (50);";
//        String sql = "select * from rule_submit_info " +
//                "where business_id=19;";
        System.out.println("mysql-sql="+sql);
        if (connection != null) {
            ps = this.connection.prepareStatement(sql);
            System.out.println("mysql-prepareStatement="+ps);
        }
    }

    @Override
    public void run(SourceContext ctx) throws InterruptedException {
        List<RuleBean> list = new ArrayList<>();
        while (isRunning) {
            try (ResultSet resultSet = ps.executeQuery()) {
                while (resultSet.next()) {
                    String paramStr = resultSet.getString("param");
                    JSONObject paramJsonOb = JSON.parseObject(paramStr);
                    RuleParamBean paramOb = JSON.toJavaObject(paramJsonOb, RuleParamBean.class);
//                    System.out.println("paramOb="+paramOb);
                    RuleBean rule = new RuleBean().builder()
                            .ruleId(resultSet.getInt("rule_id"))
                            .topicName(resultSet.getString("topic_name"))
                            .param(paramOb)
                            .apiId(resultSet.getString("api_id"))
                            .businessId(resultSet.getInt("business_id"))
                            .startTime(resultSet.getDate("start_time").getTime())
                            .expireTime(resultSet.getDate("expire_time").getTime())
                            .userId(resultSet.getInt("user_id"))
                            .build();
                    list.add(rule);
                }
                logger.info("MY-INFO-MySqlSource rule list size = {}", list.size());
//                logger.info("MY-INFO-MySqlSource rule list=" + list.toString());
//                System.out.println("MY-INFO-rule list size =" + list.size());
                ctx.collect(list);
                list.clear();
            } catch (Exception e) {
                logger.error("MY-ERROR-mysql="+String.valueOf(e));
            }
            // My-Optional-Args
            int updateSec = conf.getInt("mysql.update.sec",30);
            Thread.sleep(1000 * updateSec);
        }
    }

    @Override
    public void cancel() {
        try {
            super.close();
            if (connection != null) {
                connection.close();
            }
            if (ps != null) {
                ps.close();
            }
        } catch (Exception e) {
            logger.error("MY-ERROR-mysql-cancel=", e);
        }
        isRunning = false;
    }

    private Connection getConnection(String jdbcUrl, String user, String password) {
        Connection con = null;
        try {
            Class.forName("com.mysql.jdbc.Driver");
            //注意，改成配置参数 数据库地址和用户名、密码
            System.out.println("MYINFO-MYJDBC"+jdbcUrl+"||"+user+"||"+password);
            logger.info("MYINFO-MYJDBC"+jdbcUrl+"||"+user+"||"+password);
            con = DriverManager.getConnection(jdbcUrl, user, password);
        } catch (Exception e) {
            logger.error("MYERROR-----------mysql get connection has exception , msg =", e);
        }
        return con;
    }
}
