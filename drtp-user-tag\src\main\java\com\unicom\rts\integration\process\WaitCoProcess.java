package com.unicom.rts.integration.process;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;

import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/14 10:56
 * @Description
 */
public class WaitCoProcess extends KeyedCoProcessFunction<String, MajorBean, MajorBean, Tuple3<String, String, Iterable<Header>>> {
    ParameterTool conf;
    private ValueState<String> tradeInfoState;
//    private ListState<MajorBean> listState;
    private MapState<Long,MajorBean> mapState;
    private String topic = "CB_UNIFIED_LABEL";
    public WaitCoProcess(ParameterTool conf) {
        this.conf = conf;
        topic = conf.get("event.topic", "CB_UNIFIED_LABEL");
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        tradeInfoState = getRuntimeContext().getState(new ValueStateDescriptor<String>("tradeInfoState", String.class));
//        listState = getRuntimeContext().getListState(new ListStateDescriptor<MajorBean>("listState", MajorBean.class));
        mapState = getRuntimeContext().getMapState(new MapStateDescriptor<Long,MajorBean>("listState", Long.class, MajorBean.class));
    }

    @Override
    public void onTimer(long timestamp, KeyedCoProcessFunction<String, MajorBean, MajorBean, Tuple3<String, String, Iterable<Header>>>.OnTimerContext ctx, Collector<Tuple3<String, String, Iterable<Header>>> out) throws Exception {
        super.onTimer(timestamp, ctx, out);
        String stateValue = tradeInfoState.value();
        MajorBean majorBean = mapState.get(timestamp);
        if (StringUtils.isNotBlank(stateValue)) {
            out.collect(new Tuple3<>(topic,majorBean.getSourceJsonStr(),majorBean.getHeaders()));
        }
        mapState.remove(timestamp);
    }

    @Override
    public void processElement1(MajorBean value, KeyedCoProcessFunction<String, MajorBean, MajorBean, Tuple3<String, String, Iterable<Header>>>.Context ctx, Collector<Tuple3<String, String, Iterable<Header>>> out) throws Exception {

        boolean b_TF_F_USER_SP = "TF_F_USER_SP".equals(value.getSourceJsonBean().getTable_name());
        String stateValue = tradeInfoState.value();
        long currentProcessingTime = ctx.timerService().currentProcessingTime();
        long interval = (long) (conf.getFloat("wait.sec", 60F) * 1000F);
        if (b_TF_F_USER_SP) {
            if (StringUtils.isBlank(stateValue)) {
//            listState.add(value);
                mapState.put(currentProcessingTime + interval, value);
            } else {
                Iterator<Map.Entry<Long, MajorBean>> iterator = mapState.iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Long, MajorBean> entry = iterator.next();
                    out.collect(new Tuple3<>(topic, entry.getValue().getSourceJsonStr(), entry.getValue().getHeaders()));
//                iterator.remove();
                }
                out.collect(new Tuple3<>(topic, value.getSourceJsonStr(), value.getHeaders()));
                mapState.clear();
            }
        } else {
            out.collect(new Tuple3<>(topic, value.getSourceJsonStr(), value.getHeaders()));
        }

    }

    @Override
    public void processElement2(MajorBean value, KeyedCoProcessFunction<String, MajorBean, MajorBean, Tuple3<String, String, Iterable<Header>>>.Context ctx, Collector<Tuple3<String, String, Iterable<Header>>> out) throws Exception {
//        String value1 = tradeInfoState.value();
        String operation = value.getOperation();
        RowKind rowKind = value.getRowKind();
        String userId = value.getUserId();
        if ("D".equalsIgnoreCase(operation)) {
            tradeInfoState.update(null);
            return;
        }
        if (rowKind.equals(RowKind.DELETE)) {
            tradeInfoState.update(null);
            return;
        }
        if (StringUtils.isBlank(userId)) {
            return;
        }
//        switch (rowKind) {
//            case INSERT:
//            case UPDATE_AFTER:
//
//        }
        tradeInfoState.update(userId);
    }
}
