package com.unicom.rts.integration.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/17 15:28
 * @Description
 */
@Data
public class SourceJsonBean {
    private String system;
    private String table_name;
    private String operation;

    // TF_F_USER SERIAL_NUMBER、USER_ID、PROVINCE_CODE、EPARCHY_CODE、REMOVE_TAG、OPEN_DATE 、CREDIT_VALUE 、USER_STATE_CODESET、PRODUCT_ID 、NET_TYPE_CODE
    private String SERIAL_NUMBER;
    private String USER_ID;
    private String PROVINCE_CODE;
    private String EPARCHY_CODE;
    private String REMOVE_TAG;
    private String OPEN_DATE;
    private String CREDIT_VALUE;
    private String USER_STATE_CODESET;
    private String PRODUCT_ID;
    private String NET_TYPE_CODE;
    // TF_F_USER_SP USER_ID、SP_PRODUCT_ID、START_DATE、END_DATE
    private String SP_PRODUCT_ID;
    private String START_DATE;
    private String END_DATE;
}
