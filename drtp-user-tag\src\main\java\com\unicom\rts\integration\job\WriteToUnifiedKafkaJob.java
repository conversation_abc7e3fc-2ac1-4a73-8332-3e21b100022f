package com.unicom.rts.integration.job;

import com.unicom.rts.integration.bean.MajorBean;
import com.unicom.rts.integration.process.WaitCoProcess;
import com.unicom.rts.integration.source.MyDeserializationSchema;
import com.unicom.rts.integration.utils.MyFlinkSqlUtil;
import com.unicom.rts.integration.utils.MyKafkaStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.api.bridge.java.StreamStatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/7/4 17:27
 * @Description
 */
public class WriteToUnifiedKafkaJob {
    private final static Logger log = LoggerFactory.getLogger(WriteToUnifiedKafkaJob.class);
    private final int dataSourceParallelism;
    private final String jobName;
    private final long checkPointTime;
    private final Path homePath;
    private final ParameterTool parameters;
    private final String tableName;
    private final long initDate;
    private final String msgType;

    public WriteToUnifiedKafkaJob(ParameterTool parameters) {

        this.parameters = parameters;
        this.tableName = parameters.get("tableName","tf_f_user_x").trim();
        this.msgType = parameters.get("msgType","1");
        this.initDate = parameters.getLong("initDate",0L);
        this.dataSourceParallelism = parameters.getInt("dataSourceParallelism",1);
        this.checkPointTime = parameters.getLong("checkPointTime", 600000L);
        homePath = new Path(parameters.get("pathStr","pathStr"));
        this.jobName = parameters.get("jobName","user-tag") +"_"+ msgType + "_" + tableName;
    }
    public void runJob() throws Exception {

        log.info("===============开始启动程序=============");
        // env
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        Configuration conf = new Configuration();
        conf.set(RestOptions.BIND_PORT, "8081,8089");
        // TODO local test
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(conf);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
        // access flink configuration after table environment instantiation
        TableConfig tableConfig = tableEnv.getConfig();
        // set low-level key-value options
        tableConfig.set("table.exec.sink.upsert-materialize", "NONE");
        tableConfig.set("table.exec.sink.not-null-enforce", "DROP");
        String warehousePath = parameters.get("warehouse.path", "hdfs:///user/hh_slfn2_sschj_gray/paimon");
        // TODO local test
//        warehousePath ="file:///C:/Users/<USER>/Desktop/Work/CUBD/Code/data/paimon/paimon_catalog_0_4";
        String defaultDatabase = parameters.get("default.database", "ubd_sscj_gray_flink");
        String userTableName = parameters.get("user.table.name", "ods_r_paimon_cb_all_20230705_pufc");

        // create paimon catalog
        tableEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='"+warehousePath+"',\n" +
//                "    'warehouse' = 'hdfs:///user/hh_slfn2_sschj_gray/paimon',\n" +
                "    'default-database' = '"+defaultDatabase+"'\n" +
                ");");
        tableEnv.executeSql("USE CATALOG paimon_catalog");
        env.setParallelism(Integer.parseInt(parameters.get("env.parallelism.all", "1")));


        // rocksDB
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM); // 设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        // TODO local test
        env.getCheckpointConfig().setCheckpointStorage(parameters.get("checkpointDataUri", "file:///C:\\Users\\<USER>\\Desktop\\Work\\CUBD\\Code\\checkpoints\\rts-tag\\WriteToUnifiedKafkaJob"));

        // checkpoint
        // TODO local test
        env.enableCheckpointing(TimeUnit.SECONDS.toMillis(parameters.getLong("env.cp.interval.sec", 120)));
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setMinPauseBetweenCheckpoints(TimeUnit.SECONDS.toMillis(parameters.getLong("env.cp.minPause.sec", 10)));
        checkpointConfig.setCheckpointTimeout(TimeUnit.SECONDS.toMillis(parameters.getLong("env.cp.timeout.sec", 600)));
        checkpointConfig.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // TODO local test
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(parameters.getInt("env.restart.times", 10),
                Time.of(parameters.getLong("env.restart.interval.sec", 10), TimeUnit.SECONDS))); // 尝试重启次数,重启间隔秒数

        // kafka配置文件
//        Properties srcKafkaProp = new Properties();
//        srcKafkaProp = ParameterTool.fromMap(parameters.toMap()).getProperties();
//        srcKafkaProp.setProperty("group.id", srcKafkaProp.getProperty("group.id") + jobName);

        // // flink连接kafka
        // FlinkKafkaConsumer<byte[]> consumer = new FlinkKafkaConsumer<byte[]>(
        //         Arrays.asList(srcKafkaProp.getProperty("oggTopicInfo").split(",")),
        //         new ByteArraySchema(),
        //         srcKafkaProp);
//        String brokerSever = parameters.get("source.bootstrap");
        String topic = parameters.get("source.topic", "CB_ALL");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<Row> dataStreamFromPaimon = tableEnv.toChangelogStream(tableFromPaimon);
        KeyedStream<MajorBean, String> paimonIn = dataStreamFromPaimon.flatMap(new FlatMapFunction<Row, MajorBean>() {
            @Override
            public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
                String user_id = String.valueOf(value.getField("USER_ID"));
                if (StringUtils.isBlank(user_id)) {
                    return;
                }
                MajorBean majorBean = new MajorBean();
                majorBean.setUserId(user_id);
                majorBean.setDataSource("Paimon");
                majorBean.setRowKind(value.getKind());
                majorBean.setOperation(String.valueOf(value.getField("operation")));
                out.collect(majorBean);
            }
        }).keyBy(MajorBean::getUserId);

        OutputTag<MajorBean> TF_F_USER_Tag = new OutputTag<MajorBean>("TF_F_USER_Tag"){};
        OutputTag<MajorBean> TF_F_USER_SP_Tag = new OutputTag<MajorBean>("TF_F_USER_SP_Tag"){};
        List<String> systemKafka = Arrays.asList(parameters.get("sink.kafka.system", "9900_2i").split(",", -1));
        List<String> systemPaimon = Arrays.asList(parameters.get("sink.paimon.system", "9900_2i").split(",", -1));

        // kafka source
        SingleOutputStreamOperator<MajorBean> msgStream =
                MyKafkaStream.addSource(env, parameters, topic, new MyDeserializationSchema())
                        .process(new ProcessFunction<MajorBean, MajorBean>() {
                            @Override
                            public void processElement(MajorBean value, Context ctx, Collector<MajorBean> out) throws Exception {
                                String user_Id = value.getUSER_ID();
                                if (StringUtils.isBlank(user_Id)) {
                                    return;
                                }
                                String table_name = value.getSourceJsonBean().getTable_name();
                                String system = value.getSourceJsonBean().getSystem();

                                // to kafka
                                if (systemKafka.contains(system)) {
                                    out.collect(value);
                                }
                                // to paimon
                                if (systemPaimon.contains(system)) {
                                    switch (table_name) {
                                        case "TF_F_USER":
                                            ctx.output(TF_F_USER_Tag, value);
                                            break;
                                        case "TF_F_USER_SP":
                                            ctx.output(TF_F_USER_SP_Tag, value);
                                            break;
                                        default:
                                    }
                                }
                            }
                        })
                        .uid("DispatchProcess")
                        .name("DispatchProcess")
                        .setParallelism(parameters.getInt("process.dispatch.parallelism", 1));

        // to kafka
        SingleOutputStreamOperator<Tuple3<String, String, Iterable<Header>>> outStream = msgStream
                .keyBy(MajorBean::getUSER_ID)
                .connect(paimonIn)
                .process(new WaitCoProcess(parameters))
                .uid("CoProcessForWait")
                .name("CoProcessForWait")
                .setParallelism(parameters.getInt("process.coprocess.parallelism", 1));

        MyKafkaStream.addSink(outStream, parameters);

        // run multiple INSERT queries on the registered source table and emit the result to registered sink tables
        StreamStatementSet stmtSet = tableEnv.createStatementSet();

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_TF_F_USER_SP(tableEnv, msgStream.getSideOutput(TF_F_USER_SP_Tag), parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
        List<String> insertSql_tf_f_user = MyFlinkSqlUtil.getInsertSql_TF_F_USER(tableEnv, msgStream.getSideOutput(TF_F_USER_Tag), parameters);
        for (String s : insertSql_tf_f_user) {
            stmtSet.addInsertSql(s);
        }


        // exec
        env.disableOperatorChaining();

        // TableExecute
        // attach both pipelines to StreamExecutionEnvironment
        // (the statement set will be cleared after calling this method)
        stmtSet.attachAsDataStream();
        // execute all statements together
//        TableResult tableResult2 = stmtSet.execute();
        // get job status through TableResult
//        System.out.println(tableResult2.getJobClient().get().getJobStatus());

        // StreamExecute
        // My-Optional-Args
//        env.execute(parameters.get("env.execute.name", "rts_tag_1_local"));
        env.execute(jobName);
        log.info("==============程序提交完毕=============");








/*        SingleOutputStreamOperator<Row> opt = msgStream
                .getSideOutput(TF_F_USER_Tag)
                .flatMap(new FlatMapFunction<MajorBean, Row>() {
                    @Override
                    public void flatMap(MajorBean valueIn, Collector<Row> out) throws Exception {
                        String opt = valueIn.getOperation();

                        Row row = Row.withPositions(13);
                        row.setField(0, valueIn.getSystem());
                        row.setField(1, valueIn.getTable_name());
                        row.setField(2, valueIn.getOperation());
                        row.setField(3, valueIn.getSERIAL_NUMBER());
                        row.setField(4, valueIn.getUSER_ID());
                        row.setField(5, valueIn.getPROVINCE_CODE());
                        row.setField(6, valueIn.getEPARCHY_CODE());
                        row.setField(7, valueIn.getREMOVE_TAG());
                        row.setField(8, valueIn.getOPEN_DATE());
                        row.setField(9, valueIn.getCREDIT_VALUE());
                        row.setField(10, valueIn.getUSER_STATE_CODESET());
                        row.setField(11, valueIn.getPRODUCT_ID());
                        row.setField(12, valueIn.getNET_TYPE_CODE());
//                if ("D".equals(opt)) {
//                    System.out.println(row);
//                    row.setKind(RowKind.DELETE);
//                    System.out.println("-->" + row);
//                }
                        switch (opt) {
                            case "I":
                            case "U":
//                        row.setKind(RowKind.UPDATE_AFTER);
                                row.setKind(RowKind.INSERT);
                                out.collect(row);
                                break;
                            case "D":
                                row.setKind(RowKind.DELETE);
                                out.collect(row);
                                break;
                            default:
                        }


                    }
                }).returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "table_name",
                                "operation",
                                "SERIAL_NUMBER",
                                "USER_ID",
                                "PROVINCE_CODE",
                                "EPARCHY_CODE",
                                "REMOVE_TAG",
                                "OPEN_DATE",
                                "CREDIT_VALUE",
                                "USER_STATE_CODESET",
                                "PRODUCT_ID",
                                "NET_TYPE_CODE"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING
                ));

        SingleOutputStreamOperator<Row> TF_F_USER_SP_stream = msgStream
                .getSideOutput(TF_F_USER_SP_Tag)
                .flatMap(new TF_F_USER_SP_Mapper())
                .returns(Types.ROW_NAMED(
                        new String[]{
                                "system",
                                "table_name",
                                "operation",
                                "USER_ID",
                                "SP_PRODUCT_ID",
                                "START_DATE",
                                "END_DATE",
                                "event_time",
                                "kafka_time",
                                "paimon_time"
                        },
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.STRING,
                        Types.SQL_TIMESTAMP,
                        Types.SQL_TIMESTAMP,
                        Types.SQL_TIMESTAMP
                ));


        // interpret the DataStream as a Table
        Schema schema = Schema.newBuilder()
                .column("system", "STRING")
                .column("table_name", "STRING")
                .column("operation", "STRING")
                .column("SERIAL_NUMBER", "STRING")
                .column("USER_ID", "STRING")
                .column("PROVINCE_CODE", "STRING")
                .column("EPARCHY_CODE", "STRING")
                .column("REMOVE_TAG", "STRING")
                .column("OPEN_DATE", "STRING")
                .column("CREDIT_VALUE", "STRING")
                .column("USER_STATE_CODESET", "STRING")
                .column("PRODUCT_ID", "STRING")
                .column("NET_TYPE_CODE", "STRING")
//                .column("f1", "DECIMAL(10, 2)")
//                .columnByExpression("c", "f1 - 1")
//                .column("f0", "STRING")
                .build();
        Table table_TF_F_USER = tableEnv.fromChangelogStream(opt, schema);

        // register the table under a name and perform an aggregation
        tableEnv.createTemporaryView("InputTable", table_TF_F_USER);

        // insert into paimon table from your data stream table
//        tableEnv.executeSql("INSERT INTO sink_paimon_table SELECT * FROM InputTable");

*//*        tableEnv.createTemporaryView("tmpViewFromDS_20230705",opt,
                Schema.newBuilder()
                        .column("system", "STRING")
                        .column("table_name", "STRING")
                        .column("operation", "STRING")
                        .column("SERIAL_NUMBER", "STRING")
                        .column("USER_ID", "STRING")
                        .column("PROVINCE_CODE", "STRING")
                        .column("EPARCHY_CODE", "STRING")
                        .column("REMOVE_TAG", "STRING")
                        .column("OPEN_DATE", "STRING")
                        .column("CREDIT_VALUE", "STRING")
                        .column("USER_STATE_CODESET", "STRING")
                        .column("PRODUCT_ID", "STRING")
                        .column("NET_TYPE_CODE", "STRING")
//                .column("f1", "DECIMAL(10, 2)")
//                .columnByExpression("c", "f1 - 1")
//                .column("f0", "STRING")
                        .build());*//*



        // only single INSERT query can be accepted by `addInsertSql` method
//        stmtSet.addInsertSql(
//                "INSERT INTO RubberOrders SELECT product, amount FROM Orders WHERE product LIKE '%Rubber%'");
//        stmtSet.addInsertSql(
//                "INSERT INTO GlassOrders SELECT product, amount FROM Orders WHERE product LIKE '%Glass%'");





        stmtSet.addInsertSql("INSERT INTO `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"` " +
                "select " +
                "`system`," +
                "table_name," +
                "operation," +
                "SERIAL_NUMBER," +
                "USER_ID," +
                "PROVINCE_CODE," +
                "EPARCHY_CODE," +
                "REMOVE_TAG," +
                "OPEN_DATE," +
                "CREDIT_VALUE," +
                "USER_STATE_CODESET," +
                "PRODUCT_ID," +
                "NET_TYPE_CODE," +
                "CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS event_time," +
                "CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS kafka_time," +
                "CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time \n" +
//                "DATE_FORMAT(CURRENT_TIMESTAMP as TIMESTAMP(3), 'yyyyMMdd') as pt\n" +
//                "DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyyMMdd') as pt \n" +
                "from InputTable;");*/


    }
}
