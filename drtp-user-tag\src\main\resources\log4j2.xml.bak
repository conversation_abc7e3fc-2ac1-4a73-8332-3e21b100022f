
<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--status表示log4j2本身内部信息的输出级别,默认为OFF；设置成trace,可以输出log4j2各种内部详细信息-->
<!--monitorInterval是log4j2自动检测配置修改并重新配置的间隔,单位秒-->
<configuration status="INFO" monitorInterval="30">

    <properties>
<!--        <property name="LOG_HOME">/opt/logs/hafiz/log4j2Demo/logs</property>-->
<!--        <property name="ERROR_LOG_FILE_NAME">error</property>-->
    </properties>

    <appenders>
        <!--输出日志到控制台的设置,默认SYSTEM_OUT,还有SYSTEM_ERR-->
        <console name="Console" target="SYSTEM_OUT">
            <!--日志的输出格式-->
            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n"/>
        </console>

<!--        &lt;!&ndash;指定输出的文件名和路径,append为false每次运行程序会自动清空日志,PatternLayout输出格式&ndash;&gt;-->
<!--        <File name="log" fileName="log/test.log" append="false">-->
<!--            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>-->
<!--        </File>-->

<!--        &lt;!&ndash;指定日志级别,输出日志到文件的路径,日志文件名称格式,设置超过size自动压缩&ndash;&gt;-->
<!--        <RollingFile name="RollingFileInfo" fileName="${sys:user.home}/logs/info.log"-->
<!--                     filePattern="${sys:user.home}/logs/$${date:yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log">-->
<!--            &lt;!&ndash;控制台输出日志级别,匹配就输出,不匹配的级别不输出&ndash;&gt;-->
<!--            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>-->
<!--            &lt;!&ndash;日志输出格式&ndash;&gt;-->
<!--            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n"/>-->
<!--            <Policies>-->
<!--                <TimeBasedTriggeringPolicy/>-->
<!--                &lt;!&ndash;日志文件达到size开始压缩成新文件&ndash;&gt;-->
<!--                <SizeBasedTriggeringPolicy size="100 MB"/>-->
<!--            </Policies>-->
<!--        </RollingFile>-->
<!--        <RollingFile name="RollingFileWarn" fileName="${sys:user.home}/logs/warn.log"-->
<!--                     filePattern="${sys:user.home}/logs/$${date:yyyy-MM}/warn-%d{yyyy-MM-dd}-%i.log">-->
<!--            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>-->
<!--            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n"/>-->
<!--            <Policies>-->
<!--                <TimeBasedTriggeringPolicy/>-->
<!--                <SizeBasedTriggeringPolicy size="100 MB"/>-->
<!--            </Policies>-->
<!--            &lt;!&ndash;同一文件夹下文件数&ndash;&gt;-->
<!--            <DefaultRolloverStrategy max="20"/>-->
<!--        </RollingFile>-->
<!--        <RollingFile name="RollingFileError" fileName="${sys:user.home}/logs/error.log"-->
<!--                     filePattern="${sys:user.home}/logs/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log">-->
<!--            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>-->
<!--            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n"/>-->
<!--            <Policies>-->
<!--                <TimeBasedTriggeringPolicy/>-->
<!--                <SizeBasedTriggeringPolicy size="100 MB"/>-->
<!--            </Policies>-->
<!--        </RollingFile>-->
    </appenders>

    <loggers>
        <!--指定日志输出设置-->
        <logger name="org.springframework" level="INFO"></logger>
        <!--logger的值里没有指定appender,那么使用root日志形式输出-->
        <root level="all">
            <!--指定选择上面声明的哪一个appender-->
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </root>
    </loggers>
</configuration>
