package com.unicom.rts.integration.job;

import com.unicom.rts.integration.bean.MajorBean;
import com.unicom.rts.integration.map.*;
import com.unicom.rts.integration.process.BroadcastProcess;
import com.unicom.rts.integration.process.BroadcastProcessTd2;
import com.unicom.rts.integration.process.UnionProcess;
import com.unicom.rts.integration.utils.MyFlinkSqlUtil;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.PredefinedOptions;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.api.bridge.java.StreamStatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/7/4 17:27
 * @Description
 */
public class RealtimeTagJob {
    private final static Logger log = LoggerFactory.getLogger(RealtimeTagJob.class);
    private final ParameterTool parameters;
    private final String tableName;
    StreamExecutionEnvironment env;
    StreamTableEnvironment tableEnv;
    StreamStatementSet stmtSet;
    String defaultDatabase;
    String userTableName;
    String warehousePath;

    public RealtimeTagJob(ParameterTool parameters) {

        this.parameters = parameters;
        this.tableName = parameters.get("tableName", "tf_f_user_x").trim().toLowerCase();
        defaultDatabase = parameters.get("default.database", "ubd_sscj_gray_flink");
        userTableName = parameters.get("table.name.TF_F_USER", "ods_r_paimon_tf_f_user");
        warehousePath = parameters.get("warehouse.path", "hdfs:///user/hh_slfn2_sschj_gray/paimon");
    }

    public void runJob() throws Exception {

        log.info("===============开始启动程序=============");
        // env
//        Configuration conf = new Configuration();
//        conf.set(RestOptions.BIND_PORT, "8081,8089");
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(conf);
        env = StreamExecutionEnvironment.getExecutionEnvironment();
        tableEnv = StreamTableEnvironment.create(env);
        // access flink configuration after table environment instantiation
        TableConfig tableConfig = tableEnv.getConfig();
        // set low-level key-value options
        tableConfig.set("table.exec.sink.upsert-materialize", "NONE");
        tableConfig.set("table.exec.sink.not-null-enforce", "DROP");
//        warehousePath ="file:///C:/Users/<USER>/Desktop/Work/CUBD/Code/data/paimon/paimon_catalog_0_4";
        // create paimon catalog
        tableEnv.executeSql("CREATE CATALOG paimon_catalog WITH (\n" +
                "    'type'='paimon',\n" +
                "    'warehouse'='" + warehousePath + "',\n" +
//                "    'warehouse' = 'hdfs:///user/hh_slfn2_sschj_gray/paimon',\n" +
                "    'default-database' = '" + defaultDatabase + "'\n" +
                ");");
        tableEnv.executeSql("USE CATALOG paimon_catalog");
        // run multiple INSERT queries on the registered source table and emit the result to registered sink tables
        stmtSet = tableEnv.createStatementSet();

        // rocksDB
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setPredefinedOptions(PredefinedOptions.SPINNING_DISK_OPTIMIZED_HIGH_MEM); // 设置为机械硬盘+内存模式
        env.setStateBackend(rocksDBStateBackend);
        // checkpoint
        env.enableCheckpointing(TimeUnit.SECONDS.toMillis(parameters.getLong("env.cp.interval.sec", 180)));
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointStorage(parameters.get("checkpointDataUri", "file:///C:\\Users\\<USER>\\Desktop\\Work\\CUBD\\Code\\checkpoints\\rts-tag\\WriteToUnifiedKafkaJob"));
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setMinPauseBetweenCheckpoints(TimeUnit.SECONDS.toMillis(parameters.getLong("env.cp.minPause.sec", 20)));
        checkpointConfig.setCheckpointTimeout(TimeUnit.SECONDS.toMillis(parameters.getLong("env.cp.timeout.sec", 86400)));
        checkpointConfig.setCheckpointInterval(TimeUnit.SECONDS.toMillis(180));
        checkpointConfig.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        checkpointConfig.setTolerableCheckpointFailureNumber(parameters.getInt("env.cp.tolerableFailure.num", 5));
        checkpointConfig.setMaxConcurrentCheckpoints(parameters.getInt("env.cp.maxConcurrent.num", 1));
        // restart
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(parameters.getInt("env.restart.times", 10),
                Time.of(parameters.getLong("env.restart.interval.sec", 10), TimeUnit.SECONDS))); // 尝试重启次数,重启间隔秒数

        switch (parameters.get("tableName", "tf_f_user_sp")) {
            case "tf_f_user_sp":
                // TF_F_USER_SP
                process_TF_F_USER_SP();
                break;
            case "tf_f_user_service":
                // TF_F_USER_SERVICE
                process_TF_F_USER_SERVICE();
                break;
            case "tf_f_user_discnt":
                process_TF_F_USER_DISCNT();
                break;
            case "tf_f_user_relation":
                process_TF_F_USER_RELATION();
                break;
            case "tf_f_user":
                process_TF_F_USER();
                break;
            case "tf_f_user_product":
                process_TF_F_USER_PRODUCT();
                break;
            case "tf_b_trade_product":
                process_TF_B_TRADE_PRODUCT();
                break;
            case "cmw_cust_manager_ref":
                process_CMW_CUST_MANAGER_REF();
                break;
            case "huafeiquan":
                process_HUAFEIQUAN();
                break;
            default:
        }

        // TableExecute, attach both pipelines to StreamExecutionEnvironment, (the statement set will be cleared after calling this method)
        stmtSet.attachAsDataStream();

        // exec
        env.disableOperatorChaining();
        env.setParallelism(parameters.getInt("env.parallelism.all", 1));
        // StreamExecute
        env.execute(parameters.get("env.execute.name", "rts_realtime_tag_" + tableName));
        log.info("==============程序提交完毕=============");
    }

    public void process_TF_B_TRADE_PRODUCT() {
        String userTableNameTF_B_TRADE_PRODUCT = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_b_trade_product");
        String consId = parameters.get("paimon.source.options", "'consumer-id' = 'rtag_tf_b_trade_product_lzl_20231220'");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));
//                .keyBy(MajorBean::getUserId);

        // paimon source
        Table tableFromPaimon_TF_B_TRADE_PRODUCT = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableNameTF_B_TRADE_PRODUCT + "` /*+ OPTIONS(" + consId + ") */");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_B_TRADE_PRODUCT = tableEnv
                .toChangelogStream(tableFromPaimon_TF_B_TRADE_PRODUCT)
                .flatMap(new TF_B_TRADE_PRODUCT_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonIn_TF_B_TRADE_PRODUCT
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));
        // to paimon
        List<String> insertSql_tf_f_other_in = MyFlinkSqlUtil.getInsertSql_UNSUBSCRIBE_PRODUCT_IN(tableEnv, outStream, parameters);
        for (String s : insertSql_tf_f_other_in) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_TF_F_USER_SP() {
        String userTableNameTF_F_USER_SP = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_f_user_sp");
        String consId = parameters.get("paimon.source.options", "'consumer-id' = 'rtag_tf_f_user_sp_default'");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));
//                .keyBy(MajorBean::getUserId);

        // paimon source
        Table tableFromPaimon_TF_F_USER_SP = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableNameTF_F_USER_SP + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon_TF_F_USER_SP = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableNameTF_F_USER_SP+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER_SP = tableEnv
                .toChangelogStream(tableFromPaimon_TF_F_USER_SP)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_SP_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonIn_TF_F_USER_SP
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_SP_PRODUCT_IN(tableEnv, outStream, parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_TF_F_USER_SERVICE() {
        String tableNameSub = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_f_user_service");
        String tableNameSub2 = parameters.get("table.name.TF_F_USER_X_2", "ods_r_paimon_tf_f_user_service_item");
        String consId = parameters.get("paimon.source.options", "'consumer-id' = 'rtag_tf_f_user_service_default'");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub = tableEnv
                .toChangelogStream(tableFromPaimonSub)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_SERVICE_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub2 = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub2 + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub2 = tableEnv
                .toChangelogStream(tableFromPaimonSub2)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_SERVICE_ITEM_Mapper(parameters))
                .uid("TF_F_USER_SERVICE_ITEM SubFlatMap").name("TF_F_USER_SERVICE_ITEM SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub
                .union(paimonIn_TF_F_USER)
                .union(paimonInStreamSub2)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_R_SERVICE_IN(tableEnv, outStream, parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_TF_F_USER_DISCNT() {
        String tableNameSub = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_f_user_discnt");
        String tableNameTd = parameters.get("table.name.TD_B_X", "ods_r_paimon_td_b_discnt_item");
        String consId = parameters.get("paimon.source.options", "rtag_tf_f_user_discnt_default");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub = tableEnv
                .toChangelogStream(tableFromPaimonSub)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_DISCNT_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        // paimon source
        Table tableFromPaimonTd = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameTd + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        final MapStateDescriptor<String, String> tdState = new MapStateDescriptor<>("tdState", Types.STRING, Types.STRING);
//        final MapStateDescriptor<String, Map<String,MajorBean>> tdState = new MapStateDescriptor<>("tdState", Types.STRING, Types.MAP(Types.STRING, Types.GENERIC(MajorBean.class)));
        // convert to DataStream
        BroadcastStream<MajorBean> broadcastStream = tableEnv
                .toChangelogStream(tableFromPaimonTd)
                .flatMap(new TD_B_DISCNT_Mapper(parameters))
                .uid("TdFlatMap").name("TdFlatMap").setParallelism(parameters.getInt("mapper.td.parallelism", 1))
                .broadcast(tdState);

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };
        OutputTag<String> printSinkTag2 = new OutputTag<String>("printSinkTag2") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        SingleOutputStreamOperator<MajorBean> outStreamTd = outStream
                .keyBy(MajorBean::getUserId)
                .connect(broadcastStream)
                .process(new BroadcastProcess(parameters, printSinkTag2))
                .uid("BroadcastProcess")
                .name("BroadcastProcess")
                .setParallelism(parameters.getInt("process.broadcast.parallelism", 1));

        outStreamTd.getSideOutput(printSinkTag2).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_R_DISCNT_IN(tableEnv, outStreamTd, parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_TF_F_USER_RELATION() {
        String tableNameSub = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_f_user_relation");
        String consId = parameters.get("paimon.source.options", "rtag_tf_f_user_relation_default");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub = tableEnv
                .toChangelogStream(tableFromPaimonSub)
                .keyBy(value -> value.getField("PRIMARY_USER_ID"))
                .flatMap(new TF_F_USER_RELATION_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_USER_IS_MIXED(tableEnv, outStream, parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_CMW_CUST_MANAGER_REF() {
        String consId = parameters.get("paimon.source.options", "rtag_tf_f_user_default");
        String tableNameSub2 = parameters.get("table.name.TF_F_USER_X2", "ods_r_paimon_cmw_cust_manager_ref");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub2 = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub2 + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub2 = tableEnv
                .toChangelogStream(tableFromPaimonSub2)
                .keyBy(value -> value.getField("USER_ID".toLowerCase()))
                .flatMap(new CMW_CUST_MANAGER_REF_MAPPER(parameters))
                .uid("CMW_CUST_MANAGER_REFFlatMap").name("CMW_CUST_MANAGER_REFFlatMap").setParallelism(parameters.getInt("mapper.sub2.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub2
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_cmw_cust_manager_ref = MyFlinkSqlUtil.getInsertSql_CMW_CUST_MANAGER_REF(tableEnv, outStream, parameters);
        for (String s : insertSql_cmw_cust_manager_ref) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_TF_F_USER() {
        String tableNameSub = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_f_user_item_shift");
        String tableNameTfo = parameters.get("table.name.TF_O_X", "ods_r_paimon_tf_o_leaverealfee");
        String consId = parameters.get("paimon.source.options", "rtag_tf_f_user_default");
//        String tableNameSub2 = parameters.get("table.name.TF_F_USER_X2", "ods_r_paimon_cmw_cust_manager_ref");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub = tableEnv
                .toChangelogStream(tableFromPaimonSub)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_ITEM_SHIFT_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        // paimon source
//        Table tableFromPaimonSub2 = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub2 + "` /*+ OPTIONS(" + consId + ") */");
////        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
//        // convert to DataStream
//        DataStream<MajorBean> paimonInStreamSub2 = tableEnv
//                .toChangelogStream(tableFromPaimonSub2)
//                .keyBy(value -> value.getField("USER_ID".toLowerCase()))
//                .flatMap(new CMW_CUST_MANAGER_REF_MAPPER(parameters))
//                .uid("Sub2FlatMap").name("Sub2FlatMap").setParallelism(parameters.getInt("mapper.sub2.parallelism", 1));

        // paimon source
        Table tableFromPaimonTfo = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameTfo + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamTfo = tableEnv
                .toChangelogStream(tableFromPaimonTfo)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_O_LEAVEREALFEE_Mapper(parameters))
                .uid("TfoFlatMap").name("TfoFlatMap").setParallelism(parameters.getInt("mapper.tfo.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub
//                .union(paimonInStreamSub2)
                .union(paimonInStreamTfo)
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_USER_IN(tableEnv, outStream, parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_TF_F_USER_PRODUCT() {
        String tableNameSub = parameters.get("table.name.TF_F_USER_X", "ods_r_paimon_tf_f_user_product");
        String tableNameTd = parameters.get("table.name.TD_B_X", "ods_r_paimon_td_b_product_item");
        String tableNameTd2 = parameters.get("table.name.TD_B_X_2", "ods_r_paimon_td_b_product");
        String consId = parameters.get("paimon.source.options", "rtag_tf_f_user_product_default");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub + "` /*+ OPTIONS(" + consId + ") */ WHERE END_DATE > '2025-07-15 23:59:59'");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub = tableEnv
                .toChangelogStream(tableFromPaimonSub)
                .keyBy(value -> value.getField("USER_ID"))
                .flatMap(new TF_F_USER_PRODUCT_Mapper(parameters))
                .uid("SubFlatMap").name("SubFlatMap").setParallelism(parameters.getInt("mapper.sub.parallelism", 1));

        // paimon source
        Table tableFromPaimonTd = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameTd + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        final MapStateDescriptor<String, String> tdState = new MapStateDescriptor<>("tdState", Types.STRING, Types.STRING);
//        final MapStateDescriptor<String, Map<String,MajorBean>> tdState = new MapStateDescriptor<>("tdState", Types.STRING, Types.MAP(Types.STRING, Types.GENERIC(MajorBean.class)));
        // convert to DataStream
        BroadcastStream<MajorBean> broadcastStream = tableEnv
                .toChangelogStream(tableFromPaimonTd)
                .flatMap(new TD_B_PRODUCT_ITEM_Mapper(parameters))
                .uid("TdFlatMap").name("TdFlatMap").setParallelism(parameters.getInt("mapper.td.parallelism", 1))
                .broadcast(tdState);

        Table tableFromPaimonTd2 = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameTd2 + "` /*+ OPTIONS(" + consId + ") */");
        final MapStateDescriptor<String, String> tdStateTd2 = new MapStateDescriptor<>("tdStateTd2", Types.STRING, Types.STRING);
        BroadcastStream<MajorBean> broadcastStreamTd2 = tableEnv
                .toChangelogStream(tableFromPaimonTd2)
                .keyBy(value -> value.getField("PRODUCT_ID"))
                .flatMap(new TD_B_PRODUCT_Mapper(parameters))
                .uid("TD_B_PRODUCT_FlatMap").name("TD_B_PRODUCT_FlatMap").setParallelism(parameters.getInt("td_b_product mapper.sub.parallelism", 1))
                .broadcast(tdStateTd2);

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };
        OutputTag<String> printSinkTag2 = new OutputTag<String>("printSinkTag2") {
        };
        OutputTag<String> printSinkTag3 = new OutputTag<String>("printSinkTag3") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getUserId)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        SingleOutputStreamOperator<MajorBean> outStreamTd = outStream
                .keyBy(MajorBean::getUserId)
                .connect(broadcastStream)
                .process(new BroadcastProcess(parameters, printSinkTag2))
                .uid("BroadcastProcess")
                .name("BroadcastProcess")
                .setParallelism(parameters.getInt("process.broadcast.parallelism", 1));

        SingleOutputStreamOperator<MajorBean> outStreamTd2 = outStreamTd
                .keyBy(MajorBean::getUserId)
                .connect(broadcastStreamTd2)
                .process(new BroadcastProcessTd2(parameters, printSinkTag3))
                .uid("BroadcastProcessTd2")
                .name("BroadcastProcessTd2")
                .setParallelism(parameters.getInt("process.broadcast.parallelism", 1));

        outStreamTd2.getSideOutput(printSinkTag3).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSql_tf_f_user_sp = MyFlinkSqlUtil.getInsertSql_PRODUCT_IN(tableEnv, outStreamTd2, parameters);
        for (String s : insertSql_tf_f_user_sp) {
            stmtSet.addInsertSql(s);
        }
    }

    public void process_HUAFEIQUAN() {
        String consId = parameters.get("paimon.source.options", "rtag_huafeiquan_default");
        String tableNameSub2 = parameters.get("table.name.TF_F_USER_X2", "ods_r_paimon_st_login_info");

        // paimon source
        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + userTableName + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimon = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+userTableName+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonIn_TF_F_USER = tableEnv
                .toChangelogStream(tableFromPaimon)
                .keyBy(value -> value.getField("SERIAL_NUMBER"))
                .flatMap(new TF_F_USER_Mapper(parameters))
                .uid("TF_F_USER_FlatMap").name("TF_F_USER_FlatMap").setParallelism(parameters.getInt("mapper.user.parallelism", 1));

        // paimon source
        Table tableFromPaimonSub2 = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`" + defaultDatabase + "`.`" + tableNameSub2 + "` /*+ OPTIONS(" + consId + ") */");
//        Table tableFromPaimonSub = tableEnv.sqlQuery("SELECT * FROM `paimon_catalog`.`"+defaultDatabase+"`.`"+tableNameSub+"`");
        // convert to DataStream
        DataStream<MajorBean> paimonInStreamSub2 = tableEnv
                .toChangelogStream(tableFromPaimonSub2)
                .keyBy(value -> value.getField("SERIAL_NUMBER".toLowerCase()))
                .flatMap(new CMW_CUST_MANAGER_REF_MAPPER(parameters))
                .uid("HUAFEIQUAN_MAPPER").name("HUAFEIQUAN_REFFlatMap").setParallelism(parameters.getInt("mapper.sub2.parallelism", 1));

        OutputTag<String> printSinkTag = new OutputTag<String>("printSinkTag") {
        };

        SingleOutputStreamOperator<MajorBean> outStream = paimonInStreamSub2
                .union(paimonIn_TF_F_USER)
                .keyBy(MajorBean::getSERIAL_NUMBER)
                .process(new UnionProcess(parameters, printSinkTag))
                .uid("UnionProcess")
                .name("UnionProcess")
                .setParallelism(parameters.getInt("process.union.parallelism", 1));

        outStream.getSideOutput(printSinkTag).print().setParallelism(parameters.getInt("sink.parallelism", 1));

        // to paimon
        List<String> insertSqls = MyFlinkSqlUtil.getInsertSql_HUAFEIQUAN_TAG(tableEnv, outStream, parameters);
        for (String s : insertSqls) {
            stmtSet.addInsertSql(s);
        }
    }


}
