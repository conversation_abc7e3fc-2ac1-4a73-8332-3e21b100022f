package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
public class TF_B_TRADE_PRODUCT_Mapper extends RichFlatMapFunction<Row, MajorBean> {
    ParameterTool conf;

    public TF_B_TRADE_PRODUCT_Mapper(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        long currentTimeMillis = System.currentTimeMillis();
        MajorBean majorBean = new MajorBean();
        majorBean.setSubscribeId(String.valueOf(value.getField("SUBSCRIBE_ID")));
        majorBean.setTradeId(String.valueOf(value.getField("TRADE_ID")));
        majorBean.setAcceptMonth(String.valueOf(value.getField("ACCEPT_MONTH")));
        majorBean.setUserId(String.valueOf(value.getField("USER_ID")));
        majorBean.setPRODUCT_MODE(String.valueOf(value.getField("PRODUCT_MODE")));
        majorBean.setPRODUCT_ID(String.valueOf(value.getField("PRODUCT_ID")));
        majorBean.setBRAND_CODE(String.valueOf(value.getField("BRAND_CODE")));
        majorBean.setItemId(String.valueOf(value.getField("ITEM_ID")));
        majorBean.setModifyTag(String.valueOf(value.getField("MODIFY_TAG")));
        majorBean.setSTART_DATE(String.valueOf(value.getField("START_DATE")));
        majorBean.setEND_DATE(String.valueOf(value.getField("END_DATE")));
        majorBean.setUserIdA(String.valueOf(value.getField("USER_ID_A")));
        majorBean.setEPARCHY_CODE(String.valueOf(value.getField("EPARCHY_CODE")));
        majorBean.setPROVINCE_CODE(String.valueOf(value.getField("PROVINCE_CODE")));
        majorBean.setOpt(String.valueOf(value.getField("OPT")));
        majorBean.setOptTime(String.valueOf(value.getField("OPTTIME")));
        majorBean.setIn_time(String.valueOf(value.getField("IN_TIME")));
        majorBean.setDataSource(String.valueOf(value.getField("DATASOURCE")));
        majorBean.setCdh_time(String.valueOf(value.getField("CDHTIME")));
        majorBean.setDatabase_tag(String.valueOf(value.getField("DATABASE_TAG")));
        majorBean.setMyDataSource("TF_B_TRADE_PRODUCT");
        majorBean.setRowKind(value.getKind());
        majorBean.setKafka_in_time((LocalDateTime) value.getField("kafka_in_time"));
        majorBean.setKafka_out_time((LocalDateTime) value.getField("kafka_out_time"));
        majorBean.setPaimon_time((LocalDateTime) value.getField("paimon_time"));
        majorBean.parseHeader2((Map<String, byte[]>) value.getField("headers"));
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));
        out.collect(majorBean);
    }
}
