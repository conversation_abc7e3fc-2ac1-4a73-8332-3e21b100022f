package com.unicom.rts.integration.map;

import com.unicom.rts.integration.bean.MajorBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/24 20:48
 * @Description
 */
public class TF_O_LEAVEREALFEE_Mapper extends RichFlatMapFunction<Row, MajorBean> {
    ParameterTool conf;
    public TF_O_LEAVEREALFEE_Mapper(ParameterTool conf) {
        this.conf = conf;
    }

    @Override
    public void flatMap(Row value, Collector<MajorBean> out) throws Exception {
        long currentTimeMillis = System.currentTimeMillis();
        // PRIMARY_USER_ID,PRIMARY_SERIAL_NUMBER,MEM_USER_ID,MEM_SERIAL_NUMBER,RELATION_TYPE_CODE,PRIMARY_ROLE_CODE,MEM_ROLE_CODE,BILLING_MEM_ROLE_CODE,BILLING_ADD_TAG,DISCNT_PRIORITY,MEM_SHORT_NUM,START_DATE,END_DATE,REL_ITEM_ID,UPDATE_TIME,MEM_EPARCHY_CODE,MEM_PROVINCE_CODE,PRIMARY_EPARCHY_CODE,PRIMARY_PROVINCE_CODE,opt,optTime,cdhtime
        String user_id = String.valueOf(value.getField("USER_ID"));
        if (StringUtils.isBlank(user_id)) {
            return;
        }
        MajorBean majorBean = new MajorBean();
        majorBean.setUserId(user_id);
        majorBean.setPARTITION_ID(String.valueOf(value.getField("PARTITION_ID")));
        majorBean.setLEAVE_REAL_FEE(String.valueOf(value.getField("LEAVE_REAL_FEE")));
        majorBean.setMyDataSource("TF_O_LEAVEREALFEE");
        majorBean.setRowKind(value.getKind());
        majorBean.setOpt(String.valueOf(value.getField("opt")));
        majorBean.setOptTime(String.valueOf(value.getField("opttime")));
//                majorBean.setCdhtime(String.valueOf(value.getField("cdhtime")));
        majorBean.setKafka_in_time((LocalDateTime) value.getField("kafka_in_time"));
//                majorBean.setKafka_out_time(String.valueOf(value.getField("kafka_out_time")));
//                majorBean.setPaimon_time(String.valueOf(value.getField("paimon_time")));
        majorBean.parseHeader2((Map<String, byte[]>) value.getField("headers"));
        majorBean.setProcess_time(String.valueOf(currentTimeMillis));
        out.collect(majorBean);
    }
}
